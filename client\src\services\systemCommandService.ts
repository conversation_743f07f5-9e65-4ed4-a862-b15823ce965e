// @ts-nocheck
/**
 * SystemCommandPlugin服务 (TypeScript migrated)
 * 处理系统命令执行的插件通信
 */

import apiService from './api';
import { wsService } from './websocket';

class SystemCommandService {
  constructor() {
    this.pendingRequests = new Map();
    this.setupWebSocketListener();
  }

  /**
   * 设置WebSocket监听器
   */
  setupWebSocketListener() {
    // 监听插件执行结果 (服务器转发的消息类型是plugin_execute_result)
    if (wsService) {
      wsService.onMessage('plugin_execute_result', (message) => {
        this.handlePluginResponse(message);
      });
    }
  }

  /**
   * 处理插件响应
   */
  handlePluginResponse(message) {
    const { executeId, pluginName, success, data, message: resultMessage } = message.data || {};

    // 只处理SystemCommandPlugin的消息
    if (pluginName !== 'SystemCommandPlugin') {
      return;
    }

    if (executeId && this.pendingRequests.has(executeId)) {
      const { resolve, reject, timeout } = this.pendingRequests.get(executeId);

      clearTimeout(timeout);
      this.pendingRequests.delete(executeId);

      if (success) {
        resolve({
          success: true,
          data: data,
          message: resultMessage
        });
      } else {
        const errorMessage = resultMessage || '插件执行失败';
        const error = new Error(errorMessage);

        if (errorMessage.includes('插件不存在') || errorMessage.includes('未初始化')) {
          error.type = 'PLUGIN_NOT_AVAILABLE';
        } else if (errorMessage.includes('权限') || errorMessage.includes('访问被拒绝')) {
          error.type = 'PERMISSION_DENIED';
        } else if (errorMessage.includes('超时') || errorMessage.includes('timeout')) {
          error.type = 'TIMEOUT';
        } else {
          error.type = 'EXECUTION_FAILED';
        }

        error.originalMessage = resultMessage;
        reject(error);
      }
    }
  }

  /**
   * 执行插件命令
   */
  async executeCommand(agentId, command, params) {
    return new Promise(async (resolve, reject) => {
      let executeId = null;
      let timeout = null;

      try {
        // 发送命令到Agent
        const response = await apiService.executePluginCommand(
          agentId,
          'SystemCommandPlugin',
          command,
          params
        );

        if (!response || !response.success) {
          throw new Error(response?.message || '发送命令失败');
        }

        executeId = response.data.executeId;

        // 设置超时
        timeout = setTimeout(() => {
          this.pendingRequests.delete(executeId);
          reject(new Error('命令执行超时'));
        }, 60000); // 60秒超时

        // 注册回调
        this.pendingRequests.set(executeId, { resolve, reject, timeout });

      } catch (error) {
        if (timeout) clearTimeout(timeout);
        if (executeId) this.pendingRequests.delete(executeId);
        reject(error);
      }
    });
  }

  /**
   * 执行系统命令
   */
  async executeSystemCommand(agentId, command, args = [], workingDir, timeout = 30000) {
    return this.executeCommand(agentId, 'execute_command', {
      command,
      args,
      workingDir,
      timeout
    });
  }

  /**
   * 清理待处理的请求
   */
  cleanup() {
    for (const [executeId, { timeout, reject }] of this.pendingRequests) {
      clearTimeout(timeout);
      reject(new Error('服务已清理'));
    }
    this.pendingRequests.clear();
  }
}

// 创建单例实例
const systemCommandService = new SystemCommandService();
export default systemCommandService; 