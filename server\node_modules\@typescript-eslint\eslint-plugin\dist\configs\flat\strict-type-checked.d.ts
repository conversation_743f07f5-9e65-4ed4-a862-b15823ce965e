import type { FlatConfig } from '@typescript-eslint/utils/ts-eslint';
/**
 * Contains all of `recommended`, `recommended-type-checked`, and `strict`, along with additional strict rules that require type information.
 * @see {@link https://typescript-eslint.io/users/configs#strict-type-checked}
 */
declare const _default: (plugin: FlatConfig.Plugin, parser: FlatConfig.Parser) => FlatConfig.ConfigArray;
export default _default;
//# sourceMappingURL=strict-type-checked.d.ts.map