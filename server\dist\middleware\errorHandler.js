/**
 * 错误处理中间件
 * 统一处理应用程序中的错误
 */
const logger = require('../utils/logger');
const config = require('../config/server');
const { AppError, ErrorTypes } = require('../utils/errors');
/**
 * 错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
    let error = { ...err };
    error.message = err.message;
    // 记录错误日志
    logger.error('应用程序错误:', {
        message: err.message,
        stack: err.stack,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });
    // 处理不同类型的错误
    if (err.name === 'ValidationError') {
        error = handleValidationError(err);
    }
    else if (err.name === 'JsonWebTokenError') {
        error = handleJWTError(err);
    }
    else if (err.name === 'TokenExpiredError') {
        error = handleTokenExpiredError(err);
    }
    else if (err.code === 11000) {
        error = handleDuplicateFieldError(err);
    }
    else if (err.name === 'CastError') {
        error = handleCastError(err);
    }
    // 发送错误响应
    sendErrorResponse(res, error);
};
/**
 * 处理验证错误
 */
const handleValidationError = (err) => {
    const errors = Object.values(err.errors).map(val => val.message);
    const message = `输入数据验证失败: ${errors.join(', ')}`;
    return new AppError(message, 400, ErrorTypes.VALIDATION_ERROR);
};
/**
 * 处理JWT错误
 */
const handleJWTError = () => {
    return new AppError('无效的认证令牌', 401, ErrorTypes.AUTHENTICATION_ERROR);
};
/**
 * 处理JWT过期错误
 */
const handleTokenExpiredError = () => {
    return new AppError('认证令牌已过期', 401, ErrorTypes.AUTHENTICATION_ERROR);
};
/**
 * 处理重复字段错误
 */
const handleDuplicateFieldError = (err) => {
    const field = Object.keys(err.keyValue)[0];
    const message = `${field}已存在`;
    return new AppError(message, 409, ErrorTypes.CONFLICT_ERROR);
};
/**
 * 处理类型转换错误
 */
const handleCastError = (err) => {
    const message = `无效的${err.path}: ${err.value}`;
    return new AppError(message, 400, ErrorTypes.VALIDATION_ERROR);
};
/**
 * 发送错误响应
 */
const sendErrorResponse = (res, error) => {
    const isDevelopment = process.env.NODE_ENV === 'development';
    const hideDetails = config.production?.hideErrorDetails && !isDevelopment;
    const response = {
        success: false,
        error: {
            code: error.type || error.code || ErrorTypes.INTERNAL_ERROR,
            message: error.message || '服务器内部错误'
        }
    };
    // 开发环境显示详细错误信息
    if (isDevelopment && !hideDetails) {
        response.error.stack = error.stack;
        response.error.details = error.details;
    }
    // 设置状态码
    const statusCode = error.statusCode || 500;
    res.status(statusCode).json(response);
};
/**
 * 异步错误处理包装器
 */
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
/**
 * 404错误处理
 */
const notFoundHandler = (req, res, next) => {
    const error = new AppError(`路径 ${req.originalUrl} 不存在`, 404, ErrorTypes.NOT_FOUND_ERROR);
    next(error);
};
/**
 * 未捕获异常处理
 */
const handleUncaughtException = () => {
    process.on('uncaughtException', (err) => {
        logger.error('未捕获的异常:', err);
        console.error('未捕获的异常:', err);
        // 优雅关闭
        process.exit(1);
    });
};
/**
 * 未处理的Promise拒绝
 */
const handleUnhandledRejection = () => {
    process.on('unhandledRejection', (reason, promise) => {
        logger.error('未处理的Promise拒绝:', {
            reason: reason,
            promise: promise
        });
        console.error('未处理的Promise拒绝:', reason);
        // 优雅关闭
        process.exit(1);
    });
};
/**
 * WebSocket错误处理
 */
const handleWebSocketError = (ws, error) => {
    logger.error('WebSocket错误:', {
        message: error.message,
        stack: error.stack,
        clientId: ws.clientId
    });
    // 发送错误消息给客户端
    if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify({
            type: 'error',
            data: {
                code: ErrorTypes.WEBSOCKET_ERROR,
                message: '连接发生错误'
            }
        }));
    }
};
// 初始化全局错误处理
handleUncaughtException();
handleUnhandledRejection();
module.exports = {
    errorHandler,
    asyncHandler,
    notFoundHandler,
    handleWebSocketError
};
//# sourceMappingURL=errorHandler.js.map