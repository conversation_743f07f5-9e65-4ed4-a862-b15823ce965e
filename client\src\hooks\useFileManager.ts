// @ts-nocheck
import { useState, useCallback, useRef, useEffect } from 'react';
import { fileManagerService } from '../services/fileManagerService';

export const useFileManager = (agentId) => {
  const [currentPath, setCurrentPath] = useState('');  // 空字符串表示显示驱动器列表
  const [files, setFiles] = useState([]);
  const [directories, setDirectories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pathHistory, setPathHistory] = useState(['']);  // 空字符串表示驱动器根目录

  // 使用ref来存储currentPath的最新值，避免useCallback依赖问题
  const currentPathRef = useRef(currentPath);

  // 同步currentPath到ref
  useEffect(() => {
    currentPathRef.current = currentPath;
  }, [currentPath]);

  // 创建一个稳定的listDirectory函数引用
  const listDirectoryRef = useRef();

  // 列出目录内容的实际实现
  const listDirectoryImpl = useCallback(async (path, includeHidden = false) => {
    setLoading(true);
    setError(null);

    try {
      // 使用传入的路径，如果没有传入则使用当前路径的最新值（通过ref获取）
      const targetPath = path !== undefined ? path : currentPathRef.current;
      const result = await fileManagerService.listDirectory(agentId, targetPath, includeHidden);

      if (result.success) {
        setFiles(result.data.files || []);
        setDirectories(result.data.directories || []);
        // 使用API返回的路径，如果没有则使用传入的路径
        setCurrentPath(result.data.path || targetPath);
      } else {
        setError(result.message || '获取文件列表失败');
      }
    } catch (err) {
      setError('网络错误: ' + err.message);
    } finally {
      setLoading(false);
    }
  }, [agentId]); // 只依赖agentId，避免循环依赖

  // 将实现赋值给ref，这样其他函数可以通过ref调用
  listDirectoryRef.current = listDirectoryImpl;

  // 导出的listDirectory函数
  const listDirectory = useCallback((path, includeHidden = false) => {
    return listDirectoryRef.current(path, includeHidden);
  }, []);

  // 导航到指定路径
  const navigateToPath = useCallback(async (path) => {
    // 添加到历史记录
    setPathHistory(prev => [...prev, path]);
    await listDirectory(path);
  }, [listDirectory]); // 现在listDirectory是稳定的，可以安全地包含在依赖中

  // 返回上一级
  const goBack = useCallback(() => {
    if (pathHistory.length > 1) {
      const newHistory = [...pathHistory];
      newHistory.pop(); // 移除当前路径
      const previousPath = newHistory[newHistory.length - 1];
      setPathHistory(newHistory);
      listDirectory(previousPath);
    }
  }, [pathHistory, listDirectory]); // 包含listDirectory依赖

  // 回到根目录（驱动器列表）
  const goHome = useCallback(() => {
    setPathHistory(['']);
    listDirectory('');  // 空字符串表示显示驱动器列表
  }, [listDirectory]); // 包含listDirectory依赖

  // 获取文件信息
  const getFileInfo = useCallback(async (filePath) => {
    try {
      const result = await fileManagerService.getFileInfo(agentId, filePath);
      return result;
    } catch (err) {
      setError('获取文件信息失败: ' + err.message);
      return null;
    }
  }, [agentId]);

  // 创建目录
  const createDirectory = useCallback(async (dirPath) => {
    try {
      const result = await fileManagerService.createDirectory(agentId, dirPath);

      if (result.success) {
        // 刷新当前目录，使用ref获取最新路径
        await listDirectory(currentPathRef.current);
        return true;
      } else {
        setError(result.message || '创建目录失败');
        return false;
      }
    } catch (err) {
      setError('创建目录失败: ' + err.message);
      return false;
    }
  }, [agentId, listDirectory]); // 包含listDirectory依赖

  // 删除文件或目录
  const deleteFile = useCallback(async (filePath, recursive = false) => {
    try {
      const result = await fileManagerService.deleteFile(agentId, filePath, recursive);

      if (result.success) {
        // 刷新当前目录，使用ref获取最新路径
        await listDirectory(currentPathRef.current);
        return true;
      } else {
        setError(result.message || '删除失败');
        return false;
      }
    } catch (err) {
      setError('删除失败: ' + err.message);
      return false;
    }
  }, [agentId, listDirectory]); // 包含listDirectory依赖

  // 重命名文件或目录
  const renameFile = useCallback(async (oldPath, newPath) => {
    try {
      const result = await fileManagerService.renameFile(agentId, oldPath, newPath);

      if (result.success) {
        // 刷新当前目录，使用ref获取最新路径
        await listDirectory(currentPathRef.current);
        return true;
      } else {
        setError(result.message || '重命名失败');
        return false;
      }
    } catch (err) {
      setError('重命名失败: ' + err.message);
      return false;
    }
  }, [agentId, listDirectory]); // 包含listDirectory依赖

  // 复制文件
  const copyFile = useCallback(async (sourcePath, destPath, overwrite = false) => {
    try {
      const result = await fileManagerService.copyFile(agentId, sourcePath, destPath, overwrite);

      if (result.success) {
        // 刷新当前目录，使用ref获取最新路径
        await listDirectory(currentPathRef.current);
        return true;
      } else {
        setError(result.message || '复制失败');
        return false;
      }
    } catch (err) {
      setError('复制失败: ' + err.message);
      return false;
    }
  }, [agentId, listDirectory]); // 包含listDirectory依赖

  // 移动文件
  const moveFile = useCallback(async (sourcePath, destPath) => {
    try {
      const result = await fileManagerService.moveFile(agentId, sourcePath, destPath);

      if (result.success) {
        // 刷新当前目录，使用ref获取最新路径
        await listDirectory(currentPathRef.current);
        return true;
      } else {
        setError(result.message || '移动失败');
        return false;
      }
    } catch (err) {
      setError('移动失败: ' + err.message);
      return false;
    }
  }, [agentId, listDirectory]); // 包含listDirectory依赖

  // 搜索文件
  const searchFiles = useCallback(async (searchPath, pattern, recursive = true, maxResults = 100) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await fileManagerService.searchFiles(agentId, searchPath, pattern, recursive, maxResults);
      
      if (result.success) {
        // 将搜索结果显示在当前视图中
        const searchResults = result.data.results || [];
        const resultFiles = searchResults.filter(item => !item.isDirectory);
        const resultDirs = searchResults.filter(item => item.isDirectory);
        
        setFiles(resultFiles);
        setDirectories(resultDirs);
        return true;
      } else {
        setError(result.message || '搜索失败');
        return false;
      }
    } catch (err) {
      setError('搜索失败: ' + err.message);
      return false;
    } finally {
      setLoading(false);
    }
  }, [agentId]);

  // 上传文件
  const uploadFile = useCallback(async (file, targetPath) => {
    try {
      const result = await fileManagerService.uploadFile(agentId, file, targetPath);

      if (result.success) {
        // 刷新当前目录，使用ref获取最新路径
        await listDirectory(currentPathRef.current);
        return true;
      } else {
        setError(result.message || '上传失败');
        return false;
      }
    } catch (err) {
      setError('上传失败: ' + err.message);
      return false;
    }
  }, [agentId, listDirectory]); // 包含listDirectory依赖

  // 下载文件
  const downloadFile = useCallback(async (filePath, fileName, progressCallback = null, cancelCheck = null) => {
    try {
      const result = await fileManagerService.downloadFile(agentId, filePath, progressCallback, cancelCheck);

      if (result.success) {
        // 触发浏览器下载
        fileManagerService.triggerBrowserDownload(result.blob, fileName || result.fileName);
        return true;
      } else if (result.cancelled) {
        // 用户取消下载，不设置错误状态
        return false;
      } else {
        setError(result.message || '下载失败');
        return false;
      }
    } catch (err) {
      setError('下载失败: ' + err.message);
      return false;
    }
  }, [agentId]); // 只依赖agentId

  // 取消下载
  const cancelDownload = useCallback(async (transferId) => {
    try {
      const result = await fileManagerService.cancelDownload(agentId, transferId);
      return result.success;
    } catch (err) {
      // 取消下载时的错误通常是正常的（会话可能已经结束）
      console.log('取消下载结果:', err.message);
      return true; // 返回true，因为取消操作本身是成功的
    }
  }, [agentId]);

  return {
    currentPath,
    files,
    directories,
    loading,
    error,
    pathHistory,
    listDirectory,
    navigateToPath,
    goBack,
    goHome,
    getFileInfo,
    createDirectory,
    deleteFile,
    renameFile,
    copyFile,
    moveFile,
    searchFiles,
    uploadFile,
    downloadFile,
    cancelDownload
  };
}; 