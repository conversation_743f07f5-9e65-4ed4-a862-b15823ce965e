{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,MAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC1C,MAAM,MAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAC3C,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAE5D;;GAEG;AACH,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;IACzC,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;IACvB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;IAE5B,SAAS;IACT,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE;QACpB,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,GAAG,EAAE,GAAG,CAAC,WAAW;QACpB,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;KACnC,CAAC,CAAC;IAEH,YAAY;IACZ,IAAI,GAAG,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACjC,KAAK,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC1C,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC1C,KAAK,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC;IACzC,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;QAC5B,KAAK,GAAG,yBAAyB,CAAC,GAAG,CAAC,CAAC;IAC3C,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QAClC,KAAK,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAED,SAAS;IACT,iBAAiB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAClC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,qBAAqB,GAAG,CAAC,GAAG,EAAE,EAAE;IAClC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACjE,MAAM,OAAO,GAAG,aAAa,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IACjD,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,gBAAgB,CAAC,CAAC;AACnE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,cAAc,GAAG,GAAG,EAAE;IACxB,OAAO,IAAI,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE,UAAU,CAAC,oBAAoB,CAAC,CAAC;AACzE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,uBAAuB,GAAG,GAAG,EAAE;IACjC,OAAO,IAAI,QAAQ,CAAC,SAAS,EAAE,GAAG,EAAE,UAAU,CAAC,oBAAoB,CAAC,CAAC;AACzE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,yBAAyB,GAAG,CAAC,GAAG,EAAE,EAAE;IACtC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,MAAM,OAAO,GAAG,GAAG,KAAK,KAAK,CAAC;IAC9B,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,cAAc,CAAC,CAAC;AACjE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,eAAe,GAAG,CAAC,GAAG,EAAE,EAAE;IAC5B,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,KAAK,EAAE,CAAC;IAC/C,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,gBAAgB,CAAC,CAAC;AACnE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,iBAAiB,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;IACrC,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC;IAC7D,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,EAAE,gBAAgB,IAAI,CAAC,aAAa,CAAC;IAE1E,MAAM,QAAQ,GAAG;QACb,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACH,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,UAAU,CAAC,cAAc;YAC3D,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,SAAS;SACtC;KACJ,CAAC;IAEF,eAAe;IACf,IAAI,aAAa,IAAI,CAAC,WAAW,EAAE,CAAC;QAChC,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACnC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IAC3C,CAAC;IAED,QAAQ;IACR,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC;IAE3C,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1C,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,YAAY,GAAG,CAAC,EAAE,EAAE,EAAE;IACxB,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;QACtB,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC,CAAC;AACN,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,eAAe,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;IACvC,MAAM,KAAK,GAAG,IAAI,QAAQ,CACtB,MAAM,GAAG,CAAC,WAAW,MAAM,EAC3B,GAAG,EACH,UAAU,CAAC,eAAe,CAC7B,CAAC;IACF,IAAI,CAAC,KAAK,CAAC,CAAC;AAChB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,uBAAuB,GAAG,GAAG,EAAE;IACjC,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,GAAG,EAAE,EAAE;QACpC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAC7B,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAE9B,OAAO;QACP,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,wBAAwB,GAAG,GAAG,EAAE;IAClC,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;QACjD,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE;YAC3B,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,OAAO;SACnB,CAAC,CAAC;QACH,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;QAExC,OAAO;QACP,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,oBAAoB,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;IACvC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE;QACzB,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,QAAQ,EAAE,EAAE,CAAC,QAAQ;KACxB,CAAC,CAAC;IAEH,aAAa;IACb,IAAI,EAAE,CAAC,UAAU,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;QAC5B,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;YACnB,IAAI,EAAE,OAAO;YACb,IAAI,EAAE;gBACF,IAAI,EAAE,UAAU,CAAC,eAAe;gBAChC,OAAO,EAAE,QAAQ;aACpB;SACJ,CAAC,CAAC,CAAC;IACR,CAAC;AACL,CAAC,CAAC;AAEF,YAAY;AACZ,uBAAuB,EAAE,CAAC;AAC1B,wBAAwB,EAAE,CAAC;AAE3B,MAAM,CAAC,OAAO,GAAG;IACb,YAAY;IACZ,YAAY;IACZ,eAAe;IACf,oBAAoB;CACvB,CAAC"}