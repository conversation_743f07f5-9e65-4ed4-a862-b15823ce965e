/// <reference path="../types/shims.d.ts" />
/**
 * Agent列表页面
 */

import React, { useState } from 'react';
import {
  Card,
  Button,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Input,
  InputNumber,
  Modal,
  Descriptions,
  Badge,
  Tooltip,
  App,
  Form,
  Select,
  Alert,
  Spin
} from 'antd';
import {
  EyeOutlined,
  CodeOutlined,
  DesktopOutlined,
  ClockCircleOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  SendOutlined,
  ClearOutlined,
  FolderOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useAgents } from '../contexts/AgentContext';
import { useModal } from '../hooks/useModal';
import CommonTable from '../components/common/CommonTable';
import { Agent, SystemInfo } from '../types';
import { apiService } from '../services/api';
import { fileManagerService } from '../services/fileManagerService';
import systemCommandService from '../services/systemCommandService';
import pluginStateManager from '../services/pluginStateManager';
import FileManager from '../components/FileManager/FileManager';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Search, TextArea } = Input;
const { Option } = Select;

// 处理可能的编码问题
const decodeCommandOutput = (output: string): string => {
  if (!output) return output;
  
  try {
    // 尝试检测是否是被错误编码的UTF-8
    // 如果字符串看起来像是乱码，尝试重新解码
    const hasHighBitChars = /[\u0080-\u00FF]/.test(output);
    if (hasHighBitChars) {
      // 尝试将其作为Latin-1解码后重新编码为UTF-8
      const bytes = [];
      for (let i = 0; i < output.length; i++) {
        const code = output.charCodeAt(i);
        if (code > 255) {
          // 如果包含高位Unicode字符，说明可能已经是正确编码
          return output;
        }
        bytes.push(code);
      }
      
      // 尝试将字节数组解码为UTF-8
      const uint8Array = new Uint8Array(bytes);
      const decoder = new TextDecoder('utf-8', { fatal: false });
      const decoded = decoder.decode(uint8Array);
      
      // 检查解码结果是否合理（不包含太多替换字符）
      const replacementCharCount = (decoded.match(/\uFFFD/g) || []).length;
      if (replacementCharCount < decoded.length * 0.1) {
        return decoded;
      }
    }
    
    return output;
  } catch (error) {
    console.warn('字符编码处理失败:', error);
    return output;
  }
};

const AgentListPage: React.FC = () => {
  const { modal, message } = App.useApp();
  const {
    agents,
    selectedAgent,
    isLoading,
    selectAgent,
    getSystemInfo
  } = useAgents();

  const [searchText, setSearchText] = useState('');
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [loadingSystemInfo, setLoadingSystemInfo] = useState(false);

  // 使用通用模态框Hook
  const [modals, modalActions] = useModal([
    'detail',
    'batchCommand',
    'commandHistory',
    'fileManager'
  ]);

  // 批量操作相关状态
  const [selectedAgentIds, setSelectedAgentIds] = useState<string[]>([]);
  const [batchExecuting, setBatchExecuting] = useState(false);
  const [batchResults, setBatchResults] = useState<Map<string, any>>(new Map());
  const [batchForm] = Form.useForm();

  // 命令历史记录 - 按Agent ID分组
  const [commandHistoryByAgent, setCommandHistoryByAgent] = useState<Map<string, any[]>>(new Map());
  const [selectedAgentForHistory, setSelectedAgentForHistory] = useState<Agent | null>(null);

  // 文件管理器相关状态
  const [selectedAgentForFileManager, setSelectedAgentForFileManager] = useState<Agent | null>(null);

  // 命令历史弹框内的命令执行面板状态
  const [historyAgentId, setHistoryAgentId] = useState<string | null>(null);
  const [historyCommand, setHistoryCommand] = useState<string>('');
  const [historyWorkingDir, setHistoryWorkingDir] = useState<string>('');
  const [historyTimeout, setHistoryTimeout] = useState<number>(30000);
  const [historyExecuting, setHistoryExecuting] = useState<boolean>(false);

  // 过滤Agent列表
  const filteredAgents = Array.isArray(agents) ? agents.filter(agent =>
    agent.hostname.toLowerCase().includes(searchText.toLowerCase()) ||
    agent.os.toLowerCase().includes(searchText.toLowerCase()) ||
    agent.ip.includes(searchText)
  ) : [];

  // 统计数据
  const onlineCount = Array.isArray(agents) ? agents.filter(agent => agent.status === 'online').length : 0;
  const offlineCount = Array.isArray(agents) ? agents.filter(agent => agent.status === 'offline').length : 0;
  const totalCount = Array.isArray(agents) ? agents.length : 0;
  
  // 获取选中的在线Agent
  const selectedOnlineAgents = Array.isArray(agents) ? 
    agents.filter(agent => selectedAgentIds.includes(agent.id) && agent.status === 'online') : [];
  
  // 预定义的快速命令
  const quickCommands = [
    { key: 'systeminfo', label: '系统信息', command: 'systeminfo' },
    { key: 'ipconfig', label: '网络配置', command: 'ipconfig /all' },
    { key: 'tasklist', label: '进程列表', command: 'tasklist' },
    { key: 'netstat', label: '网络连接', command: 'netstat -an' },
    { key: 'whoami', label: '当前用户', command: 'whoami /all' },
    { key: 'dir', label: '目录列表', command: 'dir C:\\' }
  ];

  // 旧的快速执行函数已移除，快捷命令仅用于在弹框内填充命令输入

  /**
   * 处理批量命令执行
   */
  const handleBatchExecute = async (values: any) => {
    if (selectedOnlineAgents.length === 0) {
      message.warning('请选择至少一个在线的Agent');
      return;
    }
    
    setBatchExecuting(true);
    setBatchResults(new Map());
    
    try {
      const results = new Map<string, any>();
      
      // 并发执行命令
      const promises = selectedOnlineAgents.map(async (agent) => {
        try {
          // 确保SystemCommandPlugin已部署
          await pluginStateManager.ensurePluginAvailable(agent.id, 'SystemCommandPlugin', (_status: string) => {});

          // 使用插件执行命令
          const result: any = await systemCommandService.executeSystemCommand(
            agent.id,
            values.command,
            [], // args
            values.workingDir || '',
            values.timeout || 30000
          );
          
          if (result) {
            // 转换插件结果格式为CommandResult格式
            const commandResult = {
              success: result.success && result.data?.success,
              exitCode: result.data?.exitCode || 0,
              stdout: result.data?.stdout || '',
              stderr: result.data?.stderr || '',
              duration: result.data?.duration || 0,
              error: result.data?.error || (result.success ? '' : result.message)
            };

            results.set(agent.id, {
              agent: agent,
              success: commandResult.success,
              result: commandResult,
              error: commandResult.success ? null : commandResult.error
            });

            // 添加到该Agent的命令历史
            const historyItem = {
              id: `${Date.now()}_${agent.id}`,
              command: values.command,
              result: commandResult,
              timestamp: new Date().toISOString(),
              agentId: agent.id,
              agentName: agent.hostname,
              type: 'batch'
            };
            
            setCommandHistoryByAgent(prev => {
              const newMap = new Map(prev);
              const agentHistory = newMap.get(agent.id) || [];
              newMap.set(agent.id, [historyItem, ...agentHistory]);
              return newMap;
            });
          } else {
            results.set(agent.id, {
              agent: agent,
              success: false,
              result: null,
              error: '命令执行失败'
            });
          }
          
        } catch (error: any) {
          results.set(agent.id, {
            agent: agent,
            success: false,
            result: null,
            error: error.message
          });
        }
      });
      
      await Promise.allSettled(promises);
      setBatchResults(results);
      
      const successCount = Array.from(results.values()).filter(r => r.success).length;
      const failCount = results.size - successCount;
      
      message.success(`批量执行完成: ${successCount} 成功, ${failCount} 失败`);
      
    } catch (error: any) {
      message.error(`批量执行失败: ${error.message}`);
    } finally {
      setBatchExecuting(false);
    }
  };

  /**
   * 清空选择
   */
  const handleClearSelection = () => {
    setSelectedAgentIds([]);
    setBatchResults(new Map());
  };



  /**
   * 获取状态标签
   */
  const getStatusTag = (status: Agent['status']) => {
    const statusConfig = {
      online: { color: 'success', text: '在线' },
      offline: { color: 'default', text: '离线' },
      connecting: { color: 'processing', text: '连接中' }
    };
    
    const config = statusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  /**
   * 查看Agent详情
   */
  const handleViewDetails = async (agent: Agent) => {
    selectAgent(agent);
    modalActions.detail.open();
    setSystemInfo(null);
    
    if (agent.status === 'online') {
      setLoadingSystemInfo(true);
      try {
        const info = await getSystemInfo(agent.id);
        setSystemInfo(info);
      } catch (error) {
        message.error('获取系统信息失败');
      } finally {
        setLoadingSystemInfo(false);
      }
    }
  };

  // 命令历史弹框内执行命令
  const handleRunHistoryCommand = async () => {
    const targetAgentId = historyAgentId || selectedAgentForHistory?.id || null;
    if (!targetAgentId) {
      message.warning('请选择目标Agent');
      return;
    }
    const agent = agents.find(a => a.id === targetAgentId);
    if (!agent || agent.status !== 'online') {
      message.error('目标Agent离线，无法执行命令');
      return;
    }

    if (!historyCommand.trim()) {
      message.warning('请输入命令');
      return;
    }

    setHistoryExecuting(true);
    try {
      let loadingMsg = message.loading('正在检查命令执行插件...', 0);
      await pluginStateManager.ensurePluginAvailable(agent.id, 'SystemCommandPlugin', (status: string) => {
        loadingMsg();
        loadingMsg = message.loading(status, 0);
      });
      loadingMsg();
      loadingMsg = message.loading('正在执行命令...', 0);

      const result: any = await systemCommandService.executeSystemCommand(
        agent.id,
        historyCommand.trim(),
        [],
        historyWorkingDir.trim() || '',
        historyTimeout || 30000
      );

      loadingMsg();

      // 记录历史
      if (result) {
        const commandResult = {
          success: result.success && result.data?.success,
          exitCode: result.data?.exitCode || 0,
          stdout: result.data?.stdout || '',
          stderr: result.data?.stderr || '',
          duration: result.data?.duration || 0,
          error: result.data?.error || (result.success ? '' : result.message)
        };

        const historyItem = {
          id: `${Date.now()}_${Math.random().toString(36).slice(2,8)}`,
          command: historyCommand.trim(),
          result: commandResult,
          timestamp: new Date().toISOString(),
          agentId: agent.id,
          agentName: agent.hostname,
          type: 'single'
        } as any;

        setCommandHistoryByAgent(prev => {
          const newMap = new Map(prev);
          const agentHistory = newMap.get(agent.id) || [];
          newMap.set(agent.id, [historyItem, ...agentHistory]);
          return newMap;
        });

        message[commandResult.success ? 'success' : 'warning'](
          commandResult.success ? '命令执行成功' : `命令执行失败: ${commandResult.error || '未知错误'}`
        );
      }
    } catch (err: any) {
      message.error(`命令执行失败: ${err.message || ''}`);
      // 失败也追加历史记录，便于审计
      const agent = agents.find(a => a.id === (historyAgentId || selectedAgentForHistory?.id || ''));
      if (agent) {
        const historyItem = {
          id: `${Date.now()}_${Math.random().toString(36).slice(2,8)}`,
          command: historyCommand.trim(),
          result: {
            success: false,
            exitCode: undefined,
            stdout: '',
            stderr: '',
            duration: 0,
            error: err.message || '执行失败'
          },
          timestamp: new Date().toISOString(),
          agentId: agent.id,
          agentName: agent.hostname,
          type: 'single'
        } as any;
        setCommandHistoryByAgent(prev => {
          const newMap = new Map(prev);
          const agentHistory = newMap.get(agent.id) || [];
          newMap.set(agent.id, [historyItem, ...agentHistory]);
          return newMap;
        });
      }
    } finally {
      setHistoryExecuting(false);
    }
  };

  /**
   * 卸载Agent
   */
  const handleUninstallAgent = (agent: Agent) => {
    if (agent.status !== 'online') {
      message.warning('Agent离线，无法执行终止操作');
      return;
    }

    modal.confirm({
      title: '确认终止Agent',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>您确定要终止以下Agent吗？</p>
          <p><strong>主机名：</strong>{agent.hostname}</p>
          <p><strong>IP地址：</strong>{agent.ip}</p>
          <p><strong>操作系统：</strong>{agent.os}</p>
          <br />
          <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
            ⚠️ 警告：此操作将终止Agent进程，Agent将断开连接！
          </p>
        </div>
      ),
      okText: '确认终止',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const result = await apiService.uninstallAgent(agent.id);
          message.success(`终止指令已发送到 ${agent.hostname}`);

          // 显示终止进度信息
          modal.info({
            title: '终止进行中',
            content: (
              <div>
                <p>终止指令已发送到 {agent.hostname}</p>
                <p>操作ID: {result.uninstallId}</p>
                <p>Agent将在几秒钟内自动终止并断开连接</p>
              </div>
            ),
            onOk: () => {
              // 依赖WS推送更新，无需手动刷新
            }
          });

        } catch (error: any) {
          console.error('终止Agent失败:', error);
          message.error(`终止失败: ${error.response?.data?.message || error.message}`);
        }
      }
    });
  };

  /**
   * 打开文件管理器
   */
  const handleOpenFileManager = async (agent: Agent) => {
    if (agent.status !== 'online') {
      message.warning('只能对在线的Agent打开文件管理器');
      return;
    }

    try {
      // 显示加载提示
      let loadingMessage = message.loading('正在检查文件管理器插件...', 0);

      // 1. 首先检查并部署FileManager插件
      await pluginStateManager.ensurePluginAvailable(agent.id, 'FileManager', (status: string) => {
        loadingMessage();
        loadingMessage = message.loading(status, 0);
      });

      // 2. 插件部署成功后打开文件管理器
      loadingMessage();
      loadingMessage = message.loading('正在打开文件管理器...', 0);

      setSelectedAgentForFileManager(agent);
      modalActions.fileManager.open();

      loadingMessage();
      message.success('文件管理器已准备就绪');
    } catch (error: any) {
      console.error('准备文件管理器失败:', error);
      message.error(`准备文件管理器失败: ${error?.message || error}`);
    }
  };







  /**
   * 表格列定义
   */
  const columns: ColumnsType<Agent> = [
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => getStatusTag(status),
      filters: [
        { text: '在线', value: 'online' },
        { text: '离线', value: 'offline' },
        { text: '连接中', value: 'connecting' }
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: '主机名',
      dataIndex: 'hostname',
      key: 'hostname',
      sorter: (a, b) => a.hostname.localeCompare(b.hostname),
      render: (hostname, record) => (
        <Space>
          <DesktopOutlined />
          <Text strong>{hostname}</Text>
          {record.isAdministrator && (
            <Tooltip title="管理员权限">
              <Badge status="warning" />
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: '操作系统',
      dataIndex: 'os',
      key: 'os',
      render: (os) => <Text>{os}</Text>,
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      render: (ip) => <Text code>{ip}</Text>,
    },
    {
      title: '架构',
      dataIndex: 'architecture',
      key: 'architecture',
      width: 80,
      render: (arch) => arch ? <Tag>{arch}</Tag> : '-',
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80,
      render: (version) => <Text type="secondary">{version}</Text>,
    },
    {
      title: '最后连接',
      dataIndex: 'lastSeen',
      key: 'lastSeen',
      width: 120,
      sorter: (a, b) => dayjs(a.lastSeen).unix() - dayjs(b.lastSeen).unix(),
      render: (lastSeen) => (
        <Tooltip title={dayjs(lastSeen).format('YYYY-MM-DD HH:mm:ss')}>
          <Space>
            <ClockCircleOutlined />
            <Text type="secondary">
              {dayjs(lastSeen).fromNow()}
            </Text>
          </Space>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="命令历史">
            <Button
              type="text"
              icon={<CodeOutlined />}
              onClick={() => {
                setSelectedAgentForHistory(record);
                setHistoryAgentId(record.id);
                setHistoryCommand('');
                setHistoryWorkingDir('');
                setHistoryTimeout(30000);
                modalActions.commandHistory.open();
              }}
              disabled={false}
            />
          </Tooltip>
          <Tooltip title="文件管理器 (自动部署插件)">
            <Button
              type="text"
              icon={<FolderOutlined />}
              onClick={() => handleOpenFileManager(record)}
              disabled={record.status !== 'online'}
            />
          </Tooltip>
          <Tooltip title="终止Agent">
            <Button
              type="text"
              icon={<DeleteOutlined />}
              disabled={record.status !== 'online'}
              danger
              onClick={() => handleUninstallAgent(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题和统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Title level={3} style={{ margin: 0 }}>
            Agent 管理
          </Title>
        </Col>
        
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="总数"
              value={totalCount}
              prefix={<DesktopOutlined />}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="在线"
              value={onlineCount}
              valueStyle={{ color: '#3f8600' }}
              prefix={<Badge status="success" />}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="离线"
              value={offlineCount}
              valueStyle={{ color: '#cf1322' }}
              prefix={<Badge status="default" />}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作栏 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Search
              placeholder="搜索主机名、操作系统或IP地址"
              allowClear
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ maxWidth: 400 }}
            />
          </Col>
          <Col>
            <Space>
              <Button
                icon={<CodeOutlined />}
                onClick={() => {
                  setSelectedAgentForHistory(null);
                  modalActions.commandHistory.open();
                }}
                disabled={commandHistoryByAgent.size === 0}
              >
                全局历史 ({Array.from(commandHistoryByAgent.values()).reduce((total, history) => total + history.length, 0)})
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 批量操作栏 */}
      {selectedAgentIds.length > 0 && (
        <Card style={{ marginBottom: '16px', backgroundColor: '#f0f9ff', borderColor: '#1890ff' }}>
          <Row gutter={16} align="middle">
            <Col flex="auto">
              <Space>
                <Text strong style={{ color: '#1890ff' }}>
                  已选择 {selectedAgentIds.length} 个Agent，其中 {selectedOnlineAgents.length} 个在线
                </Text>
              </Space>
            </Col>
            <Col>
              <Space>
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  disabled={selectedOnlineAgents.length === 0}
                  onClick={modalActions.batchCommand.open}
                >
                  批量执行命令
                </Button>
                <Button
                  icon={<ClearOutlined />}
                  onClick={handleClearSelection}
                >
                  取消选择
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>
      )}

      {/* Agent列表表格 */}
      <Card>
        <CommonTable
          columns={columns}
          dataSource={filteredAgents}
          rowKey="id"
          loading={isLoading}
          showSelection={true}
          selectionConfig={{
            selectedRowKeys: selectedAgentIds,
            onChange: (selectedRowKeys: React.Key[]) => {
              setSelectedAgentIds(selectedRowKeys as string[]);
            }
          }}
          paginationConfig={{
            total: filteredAgents.length
          }}
          scroll={{ x: 800 }}
        />
      </Card>

      {/* Agent详情模态框 */}
      <Modal
        title={`Agent 详情 - ${selectedAgent?.hostname}`}
        open={modals.detail}
        onCancel={modalActions.detail.close}
        footer={[
          <Button key="close" onClick={modalActions.detail.close}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedAgent && (
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="状态">
                {getStatusTag(selectedAgent.status)}
              </Descriptions.Item>
              <Descriptions.Item label="主机名">
                {selectedAgent.hostname}
              </Descriptions.Item>
              <Descriptions.Item label="操作系统">
                {selectedAgent.os}
              </Descriptions.Item>
              <Descriptions.Item label="IP地址">
                {selectedAgent.ip}
              </Descriptions.Item>
              <Descriptions.Item label="架构">
                {selectedAgent.architecture || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="版本">
                {selectedAgent.version}
              </Descriptions.Item>
              <Descriptions.Item label="管理员权限">
                {selectedAgent.isAdministrator ? '是' : '否'}
              </Descriptions.Item>
              <Descriptions.Item label="注册时间">
                {dayjs(selectedAgent.registeredAt).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            </Descriptions>

            {selectedAgent.status === 'online' && (
              <div>
                <Title level={5}>系统信息</Title>
                {loadingSystemInfo ? (
                  <div>正在获取系统信息...</div>
                ) : systemInfo ? (
                  <Descriptions bordered column={1}>
                    <Descriptions.Item label="CPU">
                      {systemInfo.hardware?.cpu || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="内存">
                      {systemInfo.hardware?.memory ? 
                        `${systemInfo.hardware.memory.total} (可用: ${systemInfo.hardware.memory.available})` 
                        : '-'}
                    </Descriptions.Item>
                  </Descriptions>
                ) : (
                  <div>无法获取系统信息</div>
                )}
              </div>
            )}
          </Space>
        )}
      </Modal>

      {/* 批量命令执行模态框 */}
      <Modal
        title="批量执行命令"
        open={modals.batchCommand}
        onCancel={() => {
          modalActions.batchCommand.close();
          setBatchResults(new Map());
          batchForm.resetFields();
        }}
        width={1000}
        footer={null}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* 目标Agent信息 */}
          <Alert
            message={`将在 ${selectedOnlineAgents.length} 个在线Agent上执行命令`}
            description={
              <div style={{ marginTop: 8 }}>
                <Text strong>目标Agent: </Text>
                {selectedOnlineAgents.map(agent => (
                  <Tag key={agent.id} style={{ margin: '2px' }}>
                    {agent.hostname}
                  </Tag>
                ))}
              </div>
            }
            type="info"
            showIcon
          />

          <Form
            form={batchForm}
            layout="vertical"
            onFinish={handleBatchExecute}
          >
            <Row gutter={16}>
              <Col span={16}>
                <Form.Item
                  name="command"
                  label="命令"
                  rules={[{ required: true, message: '请输入要执行的命令' }]}
                >
                  <TextArea
                    placeholder="输入要执行的命令，例如: systeminfo, ipconfig /all"
                    rows={3}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="quickCommand"
                  label="快速命令"
                >
                  <Select
                    placeholder="选择预设命令"
                    allowClear
                    onChange={(value) => {
                      if (value) {
                        const command = quickCommands.find(cmd => cmd.key === value);
                        if (command) {
                          batchForm.setFieldValue('command', command.command);
                        }
                      }
                    }}
                  >
                    {quickCommands.map(cmd => (
                      <Option key={cmd.key} value={cmd.key}>
                        {cmd.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="workingDir"
                  label="工作目录 (可选)"
                >
                  <Input placeholder="例如: C:\\Users\\<USER>\\Users\\Administrator"
                    value={historyWorkingDir}
                    onChange={(e) => setHistoryWorkingDir(e.target.value)}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={6} style={{ display: 'flex', alignItems: 'flex-end' }}>
                <Form.Item style={{ marginBottom: 0, width: '100%' }}>
                  <Button block type="primary" htmlType="submit" loading={historyExecuting} icon={<SendOutlined />}>
                    执行
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card>

        {/* 历史列表 */}
        <div style={{ maxHeight: 600, overflow: 'auto' }}>
          {(() => {
            // 获取要显示的历史记录
            const historyToShow = selectedAgentForHistory 
              ? commandHistoryByAgent.get(selectedAgentForHistory.id) || []
              : Array.from(commandHistoryByAgent.values()).flat().sort((a, b) => 
                  new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
                );
            
            return historyToShow.length === 0 ? (
              <div style={{ textAlign: 'center', padding: 40, color: '#999' }}>
                <CodeOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <div>
                  {selectedAgentForHistory 
                    ? `${selectedAgentForHistory.hostname} 暂无命令执行记录`
                    : '暂无命令执行记录'
                  }
                </div>
              </div>
            ) : (
              historyToShow.map((item) => (
                <Card
                  key={item.id}
                  size="small"
                  style={{ marginBottom: 12 }}
                  title={
                    <Space>
                      <DesktopOutlined />
                      <Text strong>{item.agentName}</Text>
                      <Tag color={item.type === 'batch' ? 'blue' : 'green'}>
                        {item.type === 'batch' ? '批量' : '单机'}
                      </Tag>
                      <Text type="secondary">
                        {dayjs(item.timestamp).format('YYYY-MM-DD HH:mm:ss')}
                      </Text>
                      {item.result && (
                        <Tag color={item.result.success ? 'success' : 'error'}>
                          {item.result.success ? '成功' : '失败'}
                        </Tag>
                      )}
                    </Space>
                  }
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Text strong>命令: </Text>
                      <Text code>{item.command}</Text>
                    </div>

                    {item.result?.stdout && (
                      <div>
                        <Text strong>输出:</Text>
                        <pre style={{
                          background: '#f6f8fa',
                          padding: 12,
                          borderRadius: 6,
                          margin: '8px 0',
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-word',
                          maxHeight: 300,
                          overflow: 'auto',
                          fontFamily: 'Consolas, "Courier New", monospace',
                          fontSize: '12px',
                          lineHeight: '1.4'
                        }}>
                          {decodeCommandOutput(item.result.stdout)}
                        </pre>
                      </div>
                    )}

                    {item.result?.stderr && (
                      <div>
                        <Text strong style={{ color: '#ff4d4f' }}>错误:</Text>
                        <pre style={{
                          background: '#fff2f0',
                          padding: 12,
                          borderRadius: 6,
                          margin: '8px 0',
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-word',
                          maxHeight: 300,
                          overflow: 'auto',
                          color: '#ff4d4f',
                          fontFamily: 'Consolas, "Courier New", monospace',
                          fontSize: '12px',
                          lineHeight: '1.4'
                        }}>
                          {decodeCommandOutput(item.result.stderr)}
                        </pre>
                      </div>
                    )}

                    {item.result && (
                      <div>
                        <Space>
                          <Text type="secondary">
                            执行时间: {item.result.duration || item.result.executionTime}ms
                          </Text>
                          <Text type="secondary">
                            退出码: {item.result.exitCode}
                          </Text>
                          {item.result.error && (
                            <Text type="danger">
                              错误: {item.result.error}
                            </Text>
                          )}
                        </Space>
                      </div>
                    )}
                  </Space>
                </Card>
              ))
            );
          })()}
        </div>
      </Modal>

      {/* 文件管理器模态框 */}
      <Modal
        title={`文件管理器 - ${selectedAgentForFileManager?.hostname || ''}`}
        open={modals.fileManager}
        onCancel={() => {
          modalActions.fileManager.close();
          setSelectedAgentForFileManager(null);
        }}
        width="90%"
        style={{ top: 20 }}
        footer={null}
        destroyOnHidden
      >
        {selectedAgentForFileManager && (
          <div style={{ height: '70vh' }}>
            <FileManager
              agentId={selectedAgentForFileManager.id}
              agentName={selectedAgentForFileManager.hostname}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AgentListPage;
