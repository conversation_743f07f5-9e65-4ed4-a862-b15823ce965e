// @ts-nocheck
import React, { useState } from 'react';
import { EditOutlined } from '@ant-design/icons';
import { Modal, Input, Button } from 'antd';

const RenameModal = ({ item, onClose, onRename }) => {
  const [newName, setNewName] = useState(item.name);
  const [renaming, setRenaming] = useState(false);

  const handleRename = async () => {
    if (!newName.trim()) {
      alert('请输入新名称');
      return;
    }

    if (newName === item.name) {
      onClose();
      return;
    }

    setRenaming(true);
    try {
      await onRename(item.path, newName.trim());
      onClose();
    } catch (error) {
      console.error('重命名失败:', error);
      alert('重命名失败: ' + error.message);
    } finally {
      setRenaming(false);
    }
  };

  return (
    <Modal
      title="重命名"
      open={true}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose} disabled={renaming}>
          取消
        </Button>,
        <Button
          key="rename"
          type="primary"
          onClick={handleRename}
          disabled={!newName.trim() || renaming}
          loading={renaming}
          icon={<EditOutlined />}
        >
          {renaming ? '重命名中...' : '重命名'}
        </Button>
      ]}
      width={400}
    >
      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 500 }}>
          原名称:
        </label>
        <div style={{ padding: '8px', background: '#f5f5f5', borderRadius: '4px', fontSize: '14px', color: '#666' }}>
          {item.name}
        </div>
      </div>

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 500 }}>
          新名称:
        </label>
        <Input
          prefix={<EditOutlined />}
          value={newName}
          onChange={(e) => setNewName(e.target.value)}
          onPressEnter={handleRename}
          autoFocus
        />
      </div>

      <div style={{ fontSize: '12px', color: '#999', padding: '8px', background: '#f9f9f9', borderRadius: '4px' }}>
        名称不能包含以下字符: \ / : * ? " &lt; &gt; |
      </div>
    </Modal>
  );
};

export default RenameModal; 