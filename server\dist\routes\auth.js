// @ts-nocheck
const express = require('express');
const { asyncHandler } = require('../middleware/errorHandler');
const { AppError, ErrorTypes } = require('../utils/errors');
const { authenticateToken, generateToken } = require('../middleware/auth');
const config = require('../config/server');
const logger = require('../utils/logger');
const router = express.Router();
// 简单内存用户来源：使用配置中的默认用户
const findUser = (username) => {
    if (!username)
        return null;
    const users = Array.isArray(config.defaultUsers) ? config.defaultUsers : [];
    return users.find(u => u.username === username) || null;
};
// 登录：校验用户名/密码并签发JWT
router.post('/login', asyncHandler(async (req, res) => {
    const { username, password } = req.body || {};
    if (!username || !password) {
        throw new AppError('用户名和密码不能为空', 400, ErrorTypes.VALIDATION_ERROR);
    }
    const user = findUser(username);
    if (!user || user.password !== password) {
        logger.logSecurity('登录失败', { username, ip: req.ip, userAgent: req.get('User-Agent') });
        throw new AppError('用户名或密码错误', 401, ErrorTypes.AUTHENTICATION_ERROR);
    }
    const token = generateToken({
        id: user.username,
        username: user.username,
        role: user.role || 'user',
        permissions: user.permissions || []
    });
    logger.info('用户登录成功', { username: user.username, ip: req.ip });
    res.json({
        success: true,
        data: {
            token,
            user: {
                id: user.username,
                username: user.username,
                role: user.role || 'user',
                permissions: user.permissions || []
            },
            expiresIn: config.jwt.expiresIn
        }
    });
}));
// 登出：需要已认证，直接返回成功
router.post('/logout', authenticateToken, asyncHandler(async (req, res) => {
    logger.info('用户登出', { username: req.user?.username, ip: req.ip });
    res.json({ success: true, message: '登出成功' });
}));
// 校验：需要已认证，返回当前用户信息
router.get('/verify', authenticateToken, asyncHandler(async (req, res) => {
    res.json({ success: true, data: req.user });
}));
// 可选：获取当前用户
router.get('/me', authenticateToken, asyncHandler(async (req, res) => {
    res.json({ success: true, data: req.user });
}));
module.exports = router;
//# sourceMappingURL=auth.js.map