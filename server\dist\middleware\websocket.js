// @ts-nocheck
/**
 * WebSocket中间件
 * 统一处理WebSocket管理器的设置和验证
 */
const { AppError, ErrorTypes } = require('../utils/errors');
/**
 * WebSocket管理器存储
 */
let wsManagerInstance = null;
/**
 * 设置WebSocket管理器实例
 * @param {Object} wsManager - WebSocket管理器实例
 */
const setWebSocketManager = (wsManager) => {
    wsManagerInstance = wsManager;
};
/**
 * 获取WebSocket管理器实例
 * @returns {Object} WebSocket管理器实例
 * @throws {AppError} 如果管理器未初始化
 */
const getWebSocketManager = () => {
    if (!wsManagerInstance) {
        throw new AppError('WebSocket管理器未初始化', 500, ErrorTypes.INTERNAL_ERROR);
    }
    return wsManagerInstance;
};
/**
 * WebSocket管理器中间件
 * 将WebSocket管理器实例注入到req对象中
 */
const injectWebSocketManager = (req, res, next) => {
    try {
        req.wsManager = getWebSocketManager();
        next();
    }
    catch (error) {
        next(error);
    }
};
/**
 * 验证Agent是否存在且在线
 * @param {string} agentId - Agent ID
 * @returns {Object} Agent信息
 * @throws {AppError} 如果Agent不存在或离线
 */
const validateAgent = (agentId) => {
    const wsManager = getWebSocketManager();
    if (!agentId) {
        throw new AppError('Agent ID不能为空', 400, ErrorTypes.VALIDATION_ERROR);
    }
    const agent = wsManager.agents.get(agentId);
    if (!agent) {
        throw new AppError(`Agent ${agentId} 不存在或已离线`, 404, ErrorTypes.NOT_FOUND_ERROR);
    }
    return agent;
};
/**
 * 获取所有连接的Agent列表
 * @returns {Array} Agent信息列表
 */
const getConnectedAgents = () => {
    const wsManager = getWebSocketManager();
    return Array.from(wsManager.agents.values()).map(agent => agent.agentInfo);
};
/**
 * 向指定Agent发送消息
 * @param {string} agentId - Agent ID
 * @param {Object} message - 要发送的消息
 * @returns {Promise} 发送结果
 */
const sendToAgent = async (agentId, message) => {
    // 验证Agent在线
    const agent = validateAgent(agentId);
    const wsManager = getWebSocketManager();
    const wsAgent = wsManager.agents.get(agentId);
    if (!wsAgent) {
        throw new AppError(`Agent ${agentId} 不存在或已离线`, 404, ErrorTypes.NOT_FOUND_ERROR);
    }
    wsManager.sendMessage(wsAgent, message);
    return true;
};
module.exports = {
    setWebSocketManager,
    getWebSocketManager,
    injectWebSocketManager,
    validateAgent,
    getConnectedAgents,
    sendToAgent
};
//# sourceMappingURL=websocket.js.map