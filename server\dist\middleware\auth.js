/**
 * 认证中间件
 * 处理JWT令牌验证和用户权限检查
 */
const jwt = require('jsonwebtoken');
const { AppError, ErrorTypes } = require('../utils/errors');
const config = require('../config/server');
const logger = require('../utils/logger');
/**
 * JWT令牌验证中间件
 */
const authenticateToken = (req, res, next) => {
    try {
        // 从请求头获取令牌
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
        if (!token) {
            throw new AppError('访问被拒绝，需要认证令牌', 401, ErrorTypes.AUTHENTICATION_ERROR);
        }
        // 验证令牌
        const decoded = jwt.verify(token, config.jwt.secret, {
            issuer: config.jwt.issuer,
            audience: config.jwt.audience
        });
        // 检查令牌是否过期
        const now = Math.floor(Date.now() / 1000);
        if (decoded.exp && decoded.exp < now) {
            throw new AppError('认证令牌已过期', 401, ErrorTypes.AUTHENTICATION_ERROR);
        }
        // 将用户信息添加到请求对象
        req.user = {
            id: decoded.userId,
            username: decoded.username,
            role: decoded.role,
            permissions: decoded.permissions || [],
            iat: decoded.iat,
            exp: decoded.exp
        };
        // 记录认证成功日志
        logger.debug('用户认证成功', {
            userId: req.user.id,
            username: req.user.username,
            ip: req.ip,
            userAgent: req.get('User-Agent')
        });
        next();
    }
    catch (error) {
        if (error instanceof jwt.JsonWebTokenError) {
            logger.logSecurity('无效JWT令牌', {
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                error: error.message
            });
            return next(new AppError('无效的认证令牌', 401, ErrorTypes.AUTHENTICATION_ERROR));
        }
        if (error instanceof jwt.TokenExpiredError) {
            logger.logSecurity('JWT令牌过期', {
                ip: req.ip,
                userAgent: req.get('User-Agent')
            });
            return next(new AppError('认证令牌已过期', 401, ErrorTypes.AUTHENTICATION_ERROR));
        }
        next(error);
    }
};
/**
 * 权限检查中间件工厂
 */
const requirePermission = (permission) => {
    return (req, res, next) => {
        try {
            if (!req.user) {
                throw new AppError('用户未认证', 401, ErrorTypes.AUTHENTICATION_ERROR);
            }
            // 管理员拥有所有权限
            if (req.user.role === 'administrator') {
                return next();
            }
            // 检查用户是否有特定权限
            const hasPermission = req.user.permissions.includes(permission) ||
                req.user.permissions.includes('*');
            if (!hasPermission) {
                logger.logSecurity('权限不足', {
                    userId: req.user.id,
                    username: req.user.username,
                    requiredPermission: permission,
                    userPermissions: req.user.permissions,
                    ip: req.ip
                });
                throw new AppError('权限不足', 403, ErrorTypes.AUTHORIZATION_ERROR);
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
/**
 * 角色检查中间件工厂
 */
const requireRole = (roles) => {
    const roleArray = Array.isArray(roles) ? roles : [roles];
    return (req, res, next) => {
        try {
            if (!req.user) {
                throw new AppError('用户未认证', 401, ErrorTypes.AUTHENTICATION_ERROR);
            }
            if (!roleArray.includes(req.user.role)) {
                logger.logSecurity('角色权限不足', {
                    userId: req.user.id,
                    username: req.user.username,
                    userRole: req.user.role,
                    requiredRoles: roleArray,
                    ip: req.ip
                });
                throw new AppError('角色权限不足', 403, ErrorTypes.AUTHORIZATION_ERROR);
            }
            next();
        }
        catch (error) {
            next(error);
        }
    };
};
/**
 * 可选认证中间件（不强制要求认证）
 */
const optionalAuth = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return next();
    }
    try {
        const decoded = jwt.verify(token, config.jwt.secret, {
            issuer: config.jwt.issuer,
            audience: config.jwt.audience
        });
        req.user = {
            id: decoded.userId,
            username: decoded.username,
            role: decoded.role,
            permissions: decoded.permissions || []
        };
    }
    catch (error) {
        // 忽略认证错误，继续处理请求
        logger.debug('可选认证失败', { error: error.message });
    }
    next();
};
/**
 * 生成JWT令牌
 */
const generateToken = (user) => {
    const payload = {
        userId: user.id,
        username: user.username,
        role: user.role,
        permissions: user.permissions || []
    };
    const options = {
        expiresIn: config.jwt.expiresIn,
        issuer: config.jwt.issuer,
        audience: config.jwt.audience
    };
    return jwt.sign(payload, config.jwt.secret, options);
};
/**
 * 验证令牌（不通过中间件）
 */
const verifyToken = (token) => {
    try {
        return jwt.verify(token, config.jwt.secret, {
            issuer: config.jwt.issuer,
            audience: config.jwt.audience
        });
    }
    catch (error) {
        throw new AppError('无效的认证令牌', 401, ErrorTypes.AUTHENTICATION_ERROR);
    }
};
/**
 * 刷新令牌
 */
const refreshToken = (token) => {
    try {
        const decoded = jwt.verify(token, config.jwt.secret, {
            issuer: config.jwt.issuer,
            audience: config.jwt.audience,
            ignoreExpiration: true // 忽略过期时间
        });
        // 检查令牌是否在可刷新时间内
        const now = Math.floor(Date.now() / 1000);
        const refreshWindow = 7 * 24 * 60 * 60; // 7天
        if (now - decoded.iat > refreshWindow) {
            throw new AppError('令牌过期时间过长，无法刷新', 401, ErrorTypes.AUTHENTICATION_ERROR);
        }
        // 生成新令牌
        return generateToken({
            id: decoded.userId,
            username: decoded.username,
            role: decoded.role,
            permissions: decoded.permissions
        });
    }
    catch (error) {
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError('令牌刷新失败', 401, ErrorTypes.AUTHENTICATION_ERROR);
    }
};
module.exports = {
    authenticateToken,
    requirePermission,
    requireRole,
    optionalAuth,
    generateToken,
    verifyToken,
    refreshToken
};
//# sourceMappingURL=auth.js.map