// @ts-nocheck
import { apiService } from './api';
import { wsService } from './websocket';

let globalWebSocketHandlersSetup = false;

class FileManagerService {
  constructor() {
    this.pendingRequests = new Map();
    this.processedMessages = new Set();
    this.isWebSocketSetup = false;
    this.unsubscribeHandlers = [];
    this.initPromise = null;
  }

  async ensureInitialized() {
    if (!this.initPromise) {
      this.initPromise = this.setupWebSocketHandlers();
    }
    return this.initPromise;
  }

  setupWebSocketHandlers() {
    if (globalWebSocketHandlersSetup) {
      return;
    }

    this.cleanupWebSocketHandlers();

    const unsubscribe = wsService.onMessage('plugin_execute_result', (message) => {
      this.handlePluginResult(message);
    });

    this.unsubscribeHandlers.push(unsubscribe);

    this.isWebSocketSetup = true;
    globalWebSocketHandlersSetup = true;
  }

  cleanupWebSocketHandlers() {
    this.unsubscribeHandlers.forEach(unsubscribe => {
      if (typeof unsubscribe === 'function') unsubscribe();
    });
    this.unsubscribeHandlers = [];
    this.isWebSocketSetup = false;
    globalWebSocketHandlersSetup = false;
  }

  resetWebSocketHandlers() {
    this.cleanupWebSocketHandlers();
    this.setupWebSocketHandlers();
  }

  handlePluginResult(message) {
    if (!message || !message.data) return;

    const { executeId, pluginName, success, data, message: resultMessage } = message.data;

    if (pluginName !== 'FileManager') return;
    if (!executeId) return;

    if (this.processedMessages.has(executeId)) return;

    const pendingRequest = this.pendingRequests.get(executeId);

    if (pendingRequest) {
      this.processedMessages.add(executeId);
      this.pendingRequests.delete(executeId);

      if (success) {
        pendingRequest.resolve({ success: true, data: data || {}, message: resultMessage || '操作成功' });
      } else {
        pendingRequest.reject(new Error(resultMessage || '插件执行失败'));
      }

      if (this.processedMessages.size > 100) {
        const messagesArray = Array.from(this.processedMessages);
        const toDelete = messagesArray.slice(0, messagesArray.length - 100);
        toDelete.forEach(id => this.processedMessages.delete(id));
      }
    }
  }

  async executeCommand(agentId, command, params = {}) {
    try {
      await this.ensureInitialized();

      return new Promise(async (resolve, reject) => {
        let executeId;
        let timeout;

        try {
          const response = await apiService.executePluginCommand(
            agentId,
            'FileManager',
            command,
            params
          );

          if (!response || !response.success) {
            throw new Error(response?.message || '发送命令失败');
          }

          executeId = response.data.executeId;

          timeout = setTimeout(() => {
            this.pendingRequests.delete(executeId);
            reject(new Error('插件执行超时'));
          }, 30000);

          this.pendingRequests.set(executeId, {
            resolve: (result) => {
              clearTimeout(timeout);
              resolve(result);
            },
            reject: (error) => {
              clearTimeout(timeout);
              reject(error);
            }
          });

        } catch (error) {
          if (timeout) clearTimeout(timeout);
          if (executeId) this.pendingRequests.delete(executeId);
          reject(error);
        }
      });
    } catch (error) {
      throw new Error(`文件管理器操作失败: ${error.message}`);
    }
  }

  // 包装快捷方法
  async listDirectory(agentId, path, includeHidden = false) {
    return this.executeCommand(agentId, 'file_list', { path, includeHidden });
  }

  async getFileInfo(agentId, filePath) {
    return this.executeCommand(agentId, 'file_info', { path: filePath });
  }

  async createDirectory(agentId, dirPath) {
    return this.executeCommand(agentId, 'file_create_dir', { path: dirPath });
  }

  async deleteFile(agentId, filePath, recursive = false) {
    return this.executeCommand(agentId, 'file_delete', { path: filePath, recursive });
  }

  async renameFile(agentId, oldPath, newPath) {
    return this.executeCommand(agentId, 'file_rename', { oldPath, newPath });
  }

  async copyFile(agentId, sourcePath, destPath, overwrite = false) {
    return this.executeCommand(agentId, 'file_copy', { sourcePath, destPath, overwrite });
  }

  async moveFile(agentId, sourcePath, destPath) {
    return this.executeCommand(agentId, 'file_move', { sourcePath, destPath });
  }

  async searchFiles(agentId, searchPath, pattern, recursive = true, maxResults = 100) {
    return this.executeCommand(agentId, 'file_search', { path: searchPath, pattern, recursive, maxResults });
  }

  async startUpload(agentId, filePath, fileSize) {
    return this.executeCommand(agentId, 'file_upload_start', { path: filePath, fileSize });
  }

  async uploadChunk(agentId, transferId, chunkData) {
    return this.executeCommand(agentId, 'file_upload_chunk', { transferId, chunkData });
  }

  async finishUpload(agentId, transferId) {
    return this.executeCommand(agentId, 'file_upload_finish', { transferId });
  }

  async cancelUpload(agentId, transferId) {
    return this.executeCommand(agentId, 'file_upload_cancel', { transferId });
  }

  async startDownload(agentId, filePath, chunkSize = 64 * 1024) {
    return this.executeCommand(agentId, 'file_download_start', { path: filePath, chunkSize });
  }

  async getDownloadChunk(agentId, transferId) {
    return this.executeCommand(agentId, 'file_download_chunk', { transferId });
  }

  async completeDownload(agentId, transferId) {
    return this.executeCommand(agentId, 'file_download_complete', { transferId });
  }

  async cancelDownload(agentId, transferId) {
    return this.executeCommand(agentId, 'file_download_cancel', { transferId });
  }

  triggerBrowserDownload(blob, fileName) {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

let fileManagerServiceInstance = null;
export const fileManagerService = (() => {
  if (!fileManagerServiceInstance) {
    fileManagerServiceInstance = new FileManagerService();
  }
  return fileManagerServiceInstance;
})(); 