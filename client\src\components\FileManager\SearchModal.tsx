// @ts-nocheck
import React, { useState } from 'react';
import { SearchOutlined } from '@ant-design/icons';
import { Modal, Input, Button, Checkbox, Select, App } from 'antd';

const SearchModal = ({ currentPath, onClose, onSearch }) => {
  const { message } = App.useApp();

  const [pattern, setPattern] = useState('');
  const [recursive, setRecursive] = useState(true);
  const [maxResults, setMaxResults] = useState(100);
  const [searching, setSearching] = useState(false);

  const handleSearch = async () => {
    if (!pattern.trim()) {
      message.warning('请输入搜索模式');
      return;
    }

    setSearching(true);
    try {
      await onSearch(pattern.trim(), recursive, maxResults);
      onClose();
    } catch (error) {
      console.error('搜索失败:', error);
      message.error('搜索失败: ' + error.message);
    } finally {
      setSearching(false);
    }
  };

  return (
    <Modal
      title="搜索文件"
      open={true}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose} disabled={searching}>
          取消
        </Button>,
        <Button
          key="search"
          type="primary"
          onClick={handleSearch}
          disabled={!pattern.trim() || searching}
          loading={searching}
          icon={<SearchOutlined />}
        >
          {searching ? '搜索中...' : '搜索'}
        </Button>
      ]}
      width={480}
    >

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 500 }}>
          搜索位置:
        </label>
        <div style={{ padding: '8px', background: '#f5f5f5', borderRadius: '4px', fontSize: '12px', color: '#666' }}>
          {currentPath}
        </div>
      </div>

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 500 }}>
          搜索模式:
        </label>
        <Input
          prefix={<SearchOutlined />}
          value={pattern}
          onChange={(e) => setPattern(e.target.value)}
          onPressEnter={handleSearch}
          placeholder="例如: *.txt, notepad.exe, *report*"
          autoFocus
        />
      </div>

      <div style={{ marginBottom: '16px' }}>
        <Checkbox
          checked={recursive}
          onChange={(e) => setRecursive(e.target.checked)}
        >
          包含子文件夹
        </Checkbox>
      </div>

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 500 }}>
          最大结果数:
        </label>
        <Select
          value={maxResults}
          onChange={setMaxResults}
          style={{ width: '100%' }}
        >
          <Select.Option value={50}>50</Select.Option>
          <Select.Option value={100}>100</Select.Option>
          <Select.Option value={200}>200</Select.Option>
          <Select.Option value={500}>500</Select.Option>
        </Select>
      </div>

      <div style={{ fontSize: '12px', color: '#999', padding: '12px', background: '#f9f9f9', borderRadius: '4px' }}>
        <div style={{ marginBottom: '4px', fontWeight: 500 }}>支持的通配符:</div>
        <div>• * 匹配任意字符</div>
        <div>• ? 匹配单个字符</div>
        <div>• *.txt 匹配所有txt文件</div>
      </div>
    </Modal>
  );
};

export default SearchModal; 