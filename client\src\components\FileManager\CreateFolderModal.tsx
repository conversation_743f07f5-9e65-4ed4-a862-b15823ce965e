// @ts-nocheck
import React, { useState } from 'react';
import { FolderAddOutlined } from '@ant-design/icons';
import { Button, Modal, Input } from 'antd';

const CreateFolderModal = ({ currentPath, onClose, onCreate }) => {
  const [folderName, setFolderName] = useState('');
  const [creating, setCreating] = useState(false);

  const handleCreate = async () => {
    if (!folderName.trim()) {
      alert('请输入文件夹名称');
      return;
    }

    setCreating(true);
    try {
      await onCreate(folderName.trim());
      onClose();
    } catch (error) {
      console.error('创建文件夹失败:', error);
      alert('创建文件夹失败: ' + error.message);
    } finally {
      setCreating(false);
    }
  };

  return (
    <Modal
      title="新建文件夹"
      open={true}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose} disabled={creating}>
          取消
        </Button>,
        <Button
          key="create"
          type="primary"
          onClick={handleCreate}
          disabled={!folderName.trim() || creating}
          loading={creating}
          icon={<FolderAddOutlined />}
        >
          {creating ? '创建中...' : '创建'}
        </Button>
      ]}
      width={400}
    >
      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 500 }}>
          当前位置:
        </label>
        <div style={{ padding: '8px', background: '#f5f5f5', borderRadius: '4px', fontSize: '12px', color: '#666' }}>
          {currentPath}
        </div>
      </div>

      <div style={{ marginBottom: '16px' }}>
        <label style={{ display: 'block', marginBottom: '8px', fontWeight: 500 }}>
          文件夹名称:
        </label>
        <Input
          prefix={<FolderAddOutlined />}
          value={folderName}
          onChange={(e) => setFolderName(e.target.value)}
          onPressEnter={handleCreate}
          placeholder="输入文件夹名称"
          autoFocus
        />
      </div>

      <div style={{ fontSize: '12px', color: '#999', padding: '8px', background: '#f9f9f9', borderRadius: '4px' }}>
        文件夹名称不能包含以下字符: \ / : * ? " &lt; &gt; |
      </div>
    </Modal>
  );
};

export default CreateFolderModal; 