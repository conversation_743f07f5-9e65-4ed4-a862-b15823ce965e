/**
 * Agent全局状态管理Context
 * 解决多个组件重复初始化useAgents的问题
 */

import React, { createContext, useContext, useState, useEffect, useCallback, useRef, ReactNode } from 'react';
import { Agent, SystemInfo } from '../types';
import { apiService } from '../services/api';
import { wsService } from '../services/websocket';

interface AgentContextType {
  agents: Agent[];
  selectedAgent: Agent | null;
  isLoading: boolean;
  error: string | null;
  refreshAgents: () => Promise<void>;
  selectAgent: (agent: Agent | null) => void;
  getSystemInfo: (agentId: string) => Promise<SystemInfo | null>;
  clearError: () => void;
}

const AgentContext = createContext<AgentContextType | undefined>(undefined);

interface AgentProviderProps {
  children: ReactNode;
}

export const AgentProvider: React.FC<AgentProviderProps> = ({ children }) => {
  // 从localStorage恢复Agent列表
  const [agents, setAgents] = useState<Agent[]>(() => {
    try {
      const savedAgents = localStorage.getItem('winrat_agents');
      return savedAgents ? JSON.parse(savedAgents) : [];
    } catch {
      return [];
    }
  });
  
  // 从localStorage恢复选中的Agent
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(() => {
    try {
      const savedAgent = localStorage.getItem('winrat_selected_agent');
      return savedAgent ? JSON.parse(savedAgent) : null;
    } catch {
      return null;
    }
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const isInitializedRef = useRef(false);

  /**
   * 刷新Agent列表
   */
  const refreshAgents = useCallback(async () => {
    // No-op：完全依赖服务端通过WebSocket推送Agent列表
    setError(null);
  }, []);

  /**
   * 选择Agent
   */
  const selectAgent = useCallback((agent: Agent | null) => {
    setSelectedAgent(agent);
    // 保存到localStorage
    if (agent) {
      localStorage.setItem('winrat_selected_agent', JSON.stringify(agent));
    } else {
      localStorage.removeItem('winrat_selected_agent');
    }
  }, []);

  /**
   * 获取系统信息
   */
  const getSystemInfo = useCallback(async (agentId: string): Promise<SystemInfo | null> => {
    // 通过WS点对点请求/应答
    return new Promise<SystemInfo | null>((resolve) => {
      const requestId = `sysinfo_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`;

      // 超时保护
      const timeout = setTimeout(() => {
        setError('获取系统信息超时');
        cleanup();
        resolve(null);
      }, 30000);

      const off = wsService.onMessage('system_info_response', (message) => {
        if (message.id !== requestId) return;
        try {
          clearTimeout(timeout);
          cleanup();
          resolve(message.data as SystemInfo);
        } catch {
          resolve(null);
        }
      });

      const cleanup = () => {
        try { off(); } catch {}
      };

      // 发送请求
      wsService.sendMessage({
        type: 'system_info_request',
        id: requestId,
        data: { agentId }
      });
    });
  }, []);

  /**
   * 清除错误
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 初始化WebSocket监听器（只初始化一次）
  useEffect(() => {
    if (isInitializedRef.current) return;
    
    isInitializedRef.current = true;
    console.log('AgentProvider: 初始化WebSocket监听器');

    // 监听WebSocket连接事件
    const unsubscribeConnection = wsService.onConnection((connected) => {
      if (connected) {
        console.log('WebSocket连接已建立');
      }
    });

    // 认证成功后不再主动请求Agent列表，完全依赖服务端推送
    const unsubscribeAuthSuccess = wsService.onMessage('auth_success', () => {
      console.log('WebSocket认证成功，等待服务端推送Agent列表');
    });

    // 监听Agent连接事件
    const unsubscribeAgentConnected = wsService.onMessage('agent_connected', (message) => {
      const newAgent = message.data as Agent;
      setAgents(prev => {
        const exists = prev.find(agent => agent.id === newAgent.id);
        let updatedAgents;
        if (exists) {
          updatedAgents = prev.map(agent =>
            agent.id === newAgent.id ? { ...agent, ...newAgent } : agent
          );
        } else {
          updatedAgents = [...prev, newAgent];
        }
        // 保存到localStorage
        localStorage.setItem('winrat_agents', JSON.stringify(updatedAgents));
        return updatedAgents;
      });
    });

    // 监听Agent断开事件
    const unsubscribeAgentDisconnected = wsService.onMessage('agent_disconnected', (message) => {
      const agentId = message.data.agentId;
      setAgents(prev => {
        const updatedAgents = prev.map(agent => 
          agent.id === agentId 
            ? { ...agent, status: 'offline' as const }
            : agent
        );
        // 保存到localStorage
        localStorage.setItem('winrat_agents', JSON.stringify(updatedAgents));
        return updatedAgents;
      });
    });

    // 监听Agent列表更新
    const unsubscribeAgentList = wsService.onMessage('agent_list', (message) => {
      const agentList = message.data.agents as Agent[];
      const agents = Array.isArray(agentList) ? agentList : [];
      console.log('WebSocket收到Agent列表:', agents);
      setAgents(agents);
      // 保存到localStorage
      localStorage.setItem('winrat_agents', JSON.stringify(agents));
    });

    // 监听Agent状态更新
    const unsubscribeAgentStatus = wsService.onMessage('agent_status_update', (message) => {
      const { agentId, status } = message.data;
      setAgents(prev => {
        const updatedAgents = prev.map(agent => 
          agent.id === agentId 
            ? { ...agent, status, lastSeen: new Date().toISOString() }
            : agent
        );
        // 保存到localStorage
        localStorage.setItem('winrat_agents', JSON.stringify(updatedAgents));
        return updatedAgents;
      });
    });

    return () => {
      unsubscribeConnection();
      unsubscribeAuthSuccess();
      unsubscribeAgentConnected();
      unsubscribeAgentDisconnected();
      unsubscribeAgentList();
      unsubscribeAgentStatus();
    };
  }, []);

  // 初始化时不再触发REST拉取，完全依赖WS推送
  useEffect(() => {}, []);

  // 当选中的Agent在列表中更新时，同步更新selectedAgent
  useEffect(() => {
    if (selectedAgent) {
      const updatedAgent = agents.find(agent => agent.id === selectedAgent.id);
      if (updatedAgent && updatedAgent !== selectedAgent) {
        setSelectedAgent(updatedAgent);
      }
    }
  }, [agents, selectedAgent]);

  const value: AgentContextType = {
    agents,
    selectedAgent,
    isLoading,
    error,
    refreshAgents,
    selectAgent,
    getSystemInfo,
    clearError
  };

  return (
    <AgentContext.Provider value={value}>
      {children}
    </AgentContext.Provider>
  );
};

/**
 * 使用Agent Context的Hook
 */
export const useAgents = (): AgentContextType => {
  const context = useContext(AgentContext);
  if (context === undefined) {
    throw new Error('useAgents must be used within an AgentProvider');
  }
  return context;
};
