/**
 * WebSocket管理器
 * 负责WebSocket连接管理、消息路由和Agent管理
 */
const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid');
const { verifyToken } = require('../middleware/auth');
const { handleWebSocketError } = require('../middleware/errorHandler');
const { AppError, ErrorTypes } = require('../utils/errors');
const logger = require('../utils/logger');
const config = require('../config/server');
const CryptoService = require('./cryptoService');
class WebSocketManager {
    constructor(wss) {
        this.wss = wss;
        this.clients = new Map(); // 客户端连接
        this.agents = new Map(); // Agent连接
        this.chunkedMessages = new Map(); // 分块消息缓存
        this.cryptoService = new CryptoService();
        this.lastAgentListHash = null; // 用于智能广播，避免重复
        // 挂起的系统信息请求映射：messageId -> { requester: ws, timestamp: number }
        this.pendingSystemInfoRequests = new Map();
        this.setupWebSocketServer();
        this.startHeartbeat();
    }
    /**
     * 设置WebSocket服务器
     */
    setupWebSocketServer() {
        this.wss.on('connection', (ws, req) => {
            this.handleConnection(ws, req);
        });
        this.wss.on('error', (error) => {
            logger.error('WebSocket服务器错误:', {
                error: error.message,
                stack: error.stack,
                maxPayload: this.wss.options.maxPayload,
                maxPayloadFormatted: this.formatBytes(this.wss.options.maxPayload)
            });
        });
        logger.info('WebSocket服务器初始化完成', {
            maxPayload: this.wss.options.maxPayload,
            maxPayloadFormatted: this.formatBytes(this.wss.options.maxPayload)
        });
    }
    /**
     * 处理新连接
     */
    handleConnection(ws, req) {
        const clientId = uuidv4();
        const clientIp = req.socket.remoteAddress;
        ws.clientId = clientId;
        ws.clientIp = clientIp;
        ws.isAlive = true;
        ws.connectedAt = new Date();
        ws.lastActivity = new Date();
        logger.logWebSocket('连接建立', clientId, {
            ip: clientIp,
            userAgent: req.headers['user-agent'],
            maxPayload: this.wss.options.maxPayload,
            maxPayloadFormatted: this.formatBytes(this.wss.options.maxPayload)
        });
        // 设置消息处理
        ws.on('message', (data) => {
            // 添加消息大小监控
            const messageSize = data.length;
            const maxPayload = this.wss.options.maxPayload;
            logger.debug('收到WebSocket原始消息:', {
                clientId: ws.clientId,
                messageSize: messageSize,
                messageSizeFormatted: this.formatBytes(messageSize),
                maxPayload: maxPayload,
                maxPayloadFormatted: this.formatBytes(maxPayload),
                sizeRatio: (messageSize / maxPayload * 100).toFixed(2) + '%',
                exceedsLimit: messageSize > maxPayload
            });
            // 如果消息接近或超过限制，发出警告
            if (messageSize > maxPayload * 0.8) {
                logger.warn('WebSocket消息接近或超过大小限制:', {
                    clientId: ws.clientId,
                    messageSize: messageSize,
                    messageSizeFormatted: this.formatBytes(messageSize),
                    maxPayload: maxPayload,
                    maxPayloadFormatted: this.formatBytes(maxPayload),
                    percentage: (messageSize / maxPayload * 100).toFixed(2) + '%',
                    exceedsLimit: messageSize > maxPayload
                });
            }
            this.handleMessage(ws, data);
        });
        // 设置连接关闭处理
        ws.on('close', (code, reason) => {
            this.handleDisconnection(ws, code, reason);
        });
        // 设置错误处理
        ws.on('error', (error) => {
            logger.error('WebSocket连接错误:', {
                error: error.message,
                stack: error.stack,
                clientId: ws.clientId,
                readyState: ws.readyState,
                isPayloadError: error.message.includes('Max payload size exceeded'),
                maxPayload: this.wss.options.maxPayload,
                maxPayloadFormatted: this.formatBytes(this.wss.options.maxPayload)
            });
            handleWebSocketError(ws, error);
        });
        // 设置心跳响应
        ws.on('pong', () => {
            ws.isAlive = true;
            ws.lastActivity = new Date();
        });
        // 发送连接确认
        this.sendMessage(ws, {
            type: 'connection_established',
            data: {
                clientId: clientId,
                serverTime: new Date().toISOString()
            }
        });
    }
    /**
     * 处理消息
     */
    async handleMessage(ws, data) {
        try {
            ws.lastActivity = new Date();
            // 添加详细的消息大小调试信息
            const messageSize = data.length;
            logger.debug('处理WebSocket消息:', {
                clientId: ws.clientId,
                messageSize: messageSize,
                messageSizeFormatted: this.formatBytes(messageSize),
                dataType: typeof data,
                isBuffer: Buffer.isBuffer(data)
            });
            let message;
            try {
                const dataString = data.toString();
                logger.debug('解析JSON消息:', {
                    clientId: ws.clientId,
                    dataLength: dataString.length,
                    dataPreview: dataString.substring(0, 200) + (dataString.length > 200 ? '...' : '')
                });
                const rawMessage = JSON.parse(dataString);
                // 检查是否是分块消息
                if (rawMessage.isChunked) {
                    logger.debug('收到分块消息:', {
                        clientId: ws.clientId,
                        messageId: rawMessage.messageId,
                        chunkIndex: rawMessage.chunkIndex,
                        totalChunks: rawMessage.totalChunks
                    });
                    message = await this.handleChunkedMessage(ws, rawMessage);
                    if (!message) {
                        // 分块消息尚未完整，等待更多分块
                        return;
                    }
                }
                else {
                    // 检查是否是加密消息
                    if (rawMessage.encrypted && config.encryption.enabled) {
                        logger.debug('处理加密消息:', {
                            clientId: ws.clientId,
                            encryptedDataLength: rawMessage.data ? rawMessage.data.length : 0
                        });
                        const decryptedData = this.cryptoService.decrypt({
                            encrypted: rawMessage.data,
                            iv: '',
                            tag: ''
                        });
                        message = JSON.parse(decryptedData);
                        logger.logWebSocket('收到加密消息并解密', ws.clientId);
                    }
                    else {
                        message = rawMessage;
                    }
                }
            }
            catch (error) {
                logger.error('JSON解析失败:', {
                    clientId: ws.clientId,
                    error: error.message,
                    dataLength: data.length,
                    dataType: typeof data,
                    dataPreview: data.toString().substring(0, 100)
                });
                throw new AppError('无效的JSON格式', 400, ErrorTypes.VALIDATION_ERROR);
            }
            // 验证消息格式
            if (!message.type) {
                throw new AppError('消息缺少type字段', 400, ErrorTypes.VALIDATION_ERROR);
            }
            logger.logWebSocket('收到消息', ws.clientId, {
                type: message.type,
                hasData: !!message.data
            });
            // 根据消息类型处理
            switch (message.type) {
                case 'client_auth':
                    await this.handleClientAuth(ws, message);
                    break;
                case 'agent_register':
                    await this.handleAgentRegister(ws, message);
                    break;
                case 'agent_heartbeat':
                    await this.handleAgentHeartbeat(ws, message);
                    break;
                // 移除 agent_list_request：完全依赖服务端推送
                case 'system_info_request':
                    await this.handleSystemInfoRequest(ws, message);
                    break;
                case 'system_info_response':
                    await this.handleSystemInfoResponse(ws, message);
                    break;
                case 'uninstall_response':
                    await this.handleUninstallResponse(ws, message);
                    break;
                case 'plugin_download_response':
                    await this.handlePluginDownloadResponse(ws, message);
                    break;
                case 'plugin_execute_response':
                    await this.handlePluginExecuteResponse(ws, message);
                    break;
                default:
                    throw new AppError(`未知的消息类型: ${message.type}`, 400, ErrorTypes.VALIDATION_ERROR);
            }
        }
        catch (error) {
            logger.error('处理WebSocket消息失败:', {
                clientId: ws.clientId,
                error: error.message,
                stack: error.stack,
                messageSize: data ? data.length : 0,
                messageSizeFormatted: data ? this.formatBytes(data.length) : '0 Bytes',
                errorType: error.constructor.name,
                isPayloadError: error.message.includes('Max payload size exceeded')
            });
            // 如果是payload大小错误，提供更详细的信息
            if (error.message.includes('Max payload size exceeded')) {
                logger.error('WebSocket payload大小超限详细信息:', {
                    clientId: ws.clientId,
                    actualSize: data ? data.length : 0,
                    actualSizeFormatted: data ? this.formatBytes(data.length) : '0 Bytes',
                    maxPayload: 50 * 1024 * 1024,
                    maxPayloadFormatted: '50 MB',
                    exceedsBy: data ? data.length - (50 * 1024 * 1024) : 0,
                    exceedsByFormatted: data && data.length > (50 * 1024 * 1024) ?
                        this.formatBytes(data.length - (50 * 1024 * 1024)) : '0 Bytes'
                });
            }
            this.sendError(ws, error.message, error.code || ErrorTypes.WEBSOCKET_ERROR);
        }
    }
    /**
     * 处理客户端认证
     */
    async handleClientAuth(ws, message) {
        try {
            const { token } = message.data || {};
            if (!token) {
                throw new AppError('缺少认证令牌', 401, ErrorTypes.AUTHENTICATION_ERROR);
            }
            // 验证JWT令牌
            const decoded = verifyToken(token);
            ws.userInfo = {
                id: decoded.userId,
                username: decoded.username,
                role: decoded.role,
                permissions: decoded.permissions || []
            };
            ws.type = 'client';
            // 添加到客户端列表
            this.clients.set(ws.clientId, ws);
            logger.logWebSocket('客户端认证成功', ws.clientId, {
                username: ws.userInfo.username,
                role: ws.userInfo.role
            });
            this.sendMessage(ws, {
                type: 'auth_success',
                data: {
                    user: ws.userInfo,
                    serverInfo: {
                        version: require('../../package.json').version,
                        capabilities: ['command_execution', 'file_operations', 'system_monitoring']
                    }
                }
            });
            // 发送当前Agent列表给认证成功的客户端
            this.sendAgentList(ws);
            // 不需要立即广播，避免重复消息
            // 定时广播会处理Agent列表的更新
        }
        catch (error) {
            logger.logSecurity('客户端认证失败', {
                clientId: ws.clientId,
                ip: ws.clientIp,
                error: error.message
            });
            this.sendError(ws, '认证失败', ErrorTypes.AUTHENTICATION_ERROR);
            ws.close(1008, '认证失败');
        }
    }
    /**
     * 处理Agent注册
     */
    async handleAgentRegister(ws, message) {
        try {
            const agentInfo = message.data || {};
            // 验证Agent信息
            if (!agentInfo.hostname || !agentInfo.os) {
                throw new AppError('Agent信息不完整', 400, ErrorTypes.VALIDATION_ERROR);
            }
            ws.type = 'agent';
            ws.agentInfo = {
                id: ws.clientId,
                hostname: agentInfo.hostname,
                os: agentInfo.os,
                version: agentInfo.version || '1.0.0',
                architecture: agentInfo.architecture || 'Unknown',
                isAdministrator: agentInfo.isAdministrator || false,
                ip: ws.clientIp, // 从WebSocket连接获取的真实IP
                status: 'online',
                registeredAt: new Date(),
                lastSeen: new Date()
            };
            // 添加到Agent列表
            this.agents.set(ws.clientId, ws);
            logger.logAgent(ws.clientId, 'Agent注册', ws.agentInfo);
            this.sendMessage(ws, {
                type: 'register_success',
                data: {
                    agentId: ws.clientId,
                    serverInfo: {
                        heartbeatInterval: config.websocket.heartbeatInterval,
                        supportedCommands: config.agent.supportedCommands
                    }
                }
            });
            // 通知所有客户端有新Agent连接
            this.broadcastToClients({
                type: 'agent_connected',
                data: ws.agentInfo
            });
        }
        catch (error) {
            logger.error('Agent注册失败:', {
                clientId: ws.clientId,
                error: error.message
            });
            this.sendError(ws, 'Agent注册失败', ErrorTypes.AGENT_ERROR);
        }
    }
    /**
     * 处理Agent心跳
     */
    async handleAgentHeartbeat(ws, message) {
        if (ws.type !== 'agent') {
            throw new AppError('只有Agent可以发送心跳', 403, ErrorTypes.AUTHORIZATION_ERROR);
        }
        ws.agentInfo.lastSeen = new Date();
        ws.agentInfo.status = 'online';
        // 更新系统信息（如果提供）
        if (message.data && message.data.systemInfo) {
            ws.agentInfo.systemInfo = message.data.systemInfo;
        }
        this.sendMessage(ws, {
            type: 'heartbeat_ack',
            data: {
                timestamp: new Date().toISOString()
            }
        });
    }
    /**
     * 发送消息
     */
    sendMessage(ws, message) {
        if (ws.readyState === WebSocket.OPEN) {
            let messageObj = {
                id: uuidv4(),
                timestamp: new Date().toISOString(),
                ...message
            };
            let messageStr = JSON.stringify(messageObj);
            // 如果是Agent连接且启用加密，则加密消息
            if (ws.type === 'agent' && config.encryption.enabled) {
                try {
                    const encryptedData = this.cryptoService.encrypt(messageStr);
                    const encryptedMessage = {
                        encrypted: true,
                        data: encryptedData.encrypted
                    };
                    messageStr = JSON.stringify(encryptedMessage);
                    logger.logWebSocket('发送加密消息', ws.clientId);
                }
                catch (error) {
                    logger.error('消息加密失败，发送明文:', error);
                }
            }
            else {
                logger.logWebSocket('发送明文消息', {
                    clientId: ws.clientId,
                    type: ws.type,
                    messageType: message.type,
                    messageId: messageObj.id
                });
            }
            ws.send(messageStr);
        }
        else {
            logger.logWebSocket('WebSocket连接已关闭，无法发送消息', {
                clientId: ws.clientId,
                readyState: ws.readyState,
                messageType: message.type
            });
        }
    }
    /**
     * 发送错误消息
     */
    sendError(ws, message, code = ErrorTypes.WEBSOCKET_ERROR) {
        this.sendMessage(ws, {
            type: 'error',
            data: {
                code: code,
                message: message
            }
        });
    }
    /**
     * 广播消息给所有客户端
     */
    broadcastToClients(message) {
        logger.logWebSocket('广播消息给客户端', {
            messageType: message.type,
            clientCount: this.clients.size,
            messageId: message.id
        });
        this.clients.forEach((client) => {
            this.sendMessage(client, message);
        });
    }
    /**
     * 发送Agent列表给客户端
     */
    sendAgentList(ws) {
        const agentList = Array.from(this.agents.values()).map(agent => agent.agentInfo);
        this.sendMessage(ws, {
            type: 'agent_list',
            data: {
                agents: agentList,
                total: agentList.length
            }
        });
    }
    /**
     * 处理连接断开
     */
    handleDisconnection(ws, code, reason) {
        logger.logWebSocket('连接断开', ws.clientId, {
            code: code,
            reason: reason?.toString(),
            type: ws.type,
            duration: Date.now() - ws.connectedAt.getTime()
        });
        // 从相应列表中移除
        if (ws.type === 'client') {
            this.clients.delete(ws.clientId);
        }
        else if (ws.type === 'agent') {
            this.agents.delete(ws.clientId);
            // 通知客户端Agent断开
            this.broadcastToClients({
                type: 'agent_disconnected',
                data: {
                    agentId: ws.clientId,
                    reason: 'connection_closed'
                }
            });
        }
    }
    /**
     * 启动心跳检测
     */
    startHeartbeat() {
        // WebSocket心跳检测
        setInterval(() => {
            this.wss.clients.forEach((ws) => {
                if (!ws.isAlive) {
                    logger.logWebSocket('心跳超时，关闭连接', ws.clientId);
                    return ws.terminate();
                }
                ws.isAlive = false;
                ws.ping();
            });
        }, config.websocket.heartbeatInterval);
        // 定期广播Agent列表更新（每30秒）
        setInterval(() => {
            this.broadcastAgentList();
        }, 30000);
        // 定期清理过期的分块消息缓存（每30秒，更频繁的清理）
        setInterval(() => {
            this.cleanupExpiredChunkedMessages();
        }, 30000);
        // 定期清理过期的系统信息挂起请求（每60秒）
        setInterval(() => {
            this.cleanupExpiredPendingRequests();
        }, 60000);
    }
    /**
     * 广播Agent列表给所有客户端（智能广播，避免重复）
     */
    broadcastAgentList() {
        if (this.clients.size > 0) {
            const agentList = Array.from(this.agents.values()).map(agent => agent.agentInfo);
            const currentHash = this.calculateAgentListHash(agentList);
            // 只有Agent列表发生变化时才广播
            if (this.lastAgentListHash !== currentHash) {
                this.lastAgentListHash = currentHash;
                this.broadcastToClients({
                    type: 'agent_list',
                    data: {
                        agents: agentList,
                        total: agentList.length
                    }
                });
                console.log('Agent列表已变化，广播更新');
            }
        }
    }
    /**
     * 计算Agent列表的哈希值
     */
    calculateAgentListHash(agentList) {
        const agentIds = agentList.map(agent => `${agent.id}-${agent.status}`).sort();
        return agentIds.join('|');
    }
    // 移除 handleAgentListRequest：统一使用推送
    /**
     * 处理系统信息请求
     */
    async handleSystemInfoRequest(ws, message) {
        if (ws.type !== 'client') {
            throw new AppError('只有客户端可以请求系统信息', 403, ErrorTypes.AUTHORIZATION_ERROR);
        }
        const { agentId } = message.data || {};
        const targetAgent = this.agents.get(agentId);
        if (!targetAgent) {
            throw new AppError(`目标Agent ${agentId} 不存在`, 404, ErrorTypes.NOT_FOUND_ERROR);
        }
        // 记录挂起请求（点对点应答）
        this.pendingSystemInfoRequests.set(message.id, { requester: ws, timestamp: Date.now() });
        // 转发系统信息请求到目标Agent
        this.sendMessage(targetAgent, {
            type: 'system_info_request',
            id: message.id,
            data: message.data
        });
    }
    /**
     * 处理Agent返回的系统信息响应（点对点转发给原请求者）
     */
    async handleSystemInfoResponse(agentWs, message) {
        const pending = this.pendingSystemInfoRequests.get(message.id);
        if (!pending) {
            logger.warn('未找到匹配的系统信息请求，丢弃响应', {
                messageId: message.id,
                agentId: agentWs.clientId
            });
            return;
        }
        // 清理挂起请求
        this.pendingSystemInfoRequests.delete(message.id);
        try {
            // 点对点转发：仅发送给原请求者
            this.sendMessage(pending.requester, {
                type: 'system_info_response',
                id: message.id,
                data: {
                    ...message.data,
                    agentId: agentWs.clientId
                }
            });
        }
        catch (err) {
            logger.error('转发系统信息响应失败', {
                error: err.message,
                messageId: message.id
            });
        }
    }
    /**
     * 处理Agent卸载响应
     */
    async handleUninstallResponse(ws, message) {
        if (ws.type !== 'agent') {
            throw new AppError('只有Agent可以发送卸载响应', 403, ErrorTypes.AUTHORIZATION_ERROR);
        }
        const { status, message: responseMessage, agentId } = message.data || {};
        logger.logAgent(ws.agentInfo?.id || 'unknown', 'Agent卸载响应', {
            status: status,
            message: responseMessage,
            messageId: message.id
        });
        // 广播卸载响应给所有客户端
        this.broadcastToClients({
            type: 'agent_uninstall_response',
            id: message.id,
            data: {
                agentId: ws.agentInfo?.id || agentId,
                hostname: ws.agentInfo?.hostname || 'unknown',
                status: status,
                message: responseMessage,
                timestamp: new Date().toISOString()
            }
        });
        // 如果终止成功，准备清理Agent连接
        if (status === 'terminating') {
            logger.logAgent(ws.agentInfo?.id || 'unknown', 'Agent开始终止，准备断开连接', {
                hostname: ws.agentInfo?.hostname
            });
            // 延迟一小段时间后自动清理连接（Agent应该会自己断开）
            setTimeout(() => {
                if (this.agents.has(ws.agentInfo?.id)) {
                    logger.logAgent(ws.agentInfo?.id, 'Agent终止超时，强制断开连接');
                    this.handleDisconnection(ws, 1000, 'Agent终止完成');
                }
            }, 5000); // 5秒超时（终止比删除快）
        }
    }
    /**
     * 处理插件下载响应
     */
    async handlePluginDownloadResponse(ws, message) {
        try {
            const { data } = message;
            logger.info('收到插件下载响应:', {
                clientId: ws.clientId,
                agentId: ws.clientId,
                success: data.success,
                pluginName: data.pluginName,
                message: data.message
            });
            // 这里可以添加插件下载结果的处理逻辑
            // 例如：更新数据库中的插件状态、通知管理员等
            if (data.success) {
                logger.info(`插件 ${data.pluginName} 在Agent ${ws.clientId} 上加载成功`);
            }
            else {
                logger.error(`插件 ${data.pluginName} 在Agent ${ws.clientId} 上加载失败: ${data.message}`);
            }
        }
        catch (error) {
            logger.error('处理插件下载响应失败:', {
                clientId: ws.clientId,
                agentId: ws.clientId,
                error: error.message,
                stack: error.stack
            });
        }
    }
    /**
     * 处理插件执行响应
     */
    async handlePluginExecuteResponse(ws, message) {
        try {
            const { data } = message;
            logger.info('收到插件执行响应:', {
                clientId: ws.clientId,
                agentId: ws.clientId,
                executeId: data.executeId,
                success: data.success,
                pluginName: data.pluginName,
                command: data.command
            });
            if (data.success) {
                logger.info(`插件命令执行成功: ${data.pluginName}.${data.command}`);
                if (data.data) {
                    logger.debug('插件执行结果:', data.data);
                }
            }
            else {
                logger.error(`插件命令执行失败: ${data.pluginName}.${data.command} - ${data.message}`);
            }
            // 转发插件执行结果给所有Web客户端
            const resultMessage = {
                type: 'plugin_execute_result',
                id: data.executeId,
                data: {
                    executeId: data.executeId,
                    agentId: ws.clientId,
                    pluginName: data.pluginName,
                    command: data.command,
                    success: data.success,
                    message: data.message,
                    data: data.data,
                    timestamp: new Date().toISOString()
                }
            };
            logger.logWebSocket('转发插件执行结果给客户端', {
                executeId: data.executeId,
                agentId: ws.clientId,
                pluginName: data.pluginName,
                command: data.command,
                success: data.success,
                clientCount: this.clients.size
            });
            this.broadcastToClients(resultMessage);
        }
        catch (error) {
            logger.error('处理插件执行响应失败:', {
                clientId: ws.clientId,
                agentId: ws.clientId,
                error: error.message,
                stack: error.stack
            });
        }
    }
    /**
     * 关闭WebSocket服务
     */
    shutdown() {
        logger.info('正在关闭WebSocket服务...');
        this.wss.clients.forEach((ws) => {
            ws.close(1001, '服务器关闭');
        });
        this.wss.close();
    }
    /**
     * 处理分块消息
     */
    async handleChunkedMessage(ws, chunkMessage) {
        try {
            const { messageId, chunkIndex, totalChunks, data } = chunkMessage;
            logger.logWebSocket('收到分块消息', ws.clientId, {
                messageId,
                chunkIndex,
                totalChunks,
                chunkSize: data.length
            });
            // 初始化消息缓存
            if (!this.chunkedMessages.has(messageId)) {
                this.chunkedMessages.set(messageId, {
                    chunks: new Array(totalChunks),
                    receivedCount: 0,
                    totalChunks,
                    timestamp: Date.now(),
                    clientId: ws.clientId
                });
            }
            const messageCache = this.chunkedMessages.get(messageId);
            // 验证分块信息（只在非第一个分块时检查，因为第一个分块设置了totalChunks）
            if (messageCache.receivedCount > 0 && messageCache.totalChunks !== totalChunks) {
                throw new Error(`分块总数不匹配: 期望 ${messageCache.totalChunks}, 收到 ${totalChunks}`);
            }
            if (chunkIndex >= totalChunks) {
                throw new Error(`分块索引超出范围: ${chunkIndex} >= ${totalChunks}`);
            }
            // 存储分块数据
            if (!messageCache.chunks[chunkIndex]) {
                messageCache.chunks[chunkIndex] = data;
                messageCache.receivedCount++;
                logger.logWebSocket('分块已存储', ws.clientId, {
                    messageId,
                    chunkIndex,
                    receivedCount: messageCache.receivedCount,
                    totalChunks
                });
            }
            // 检查是否收到所有分块
            if (messageCache.receivedCount === totalChunks) {
                // 重组完整消息
                const completeMessage = messageCache.chunks.join('');
                // 清理缓存
                this.chunkedMessages.delete(messageId);
                logger.logWebSocket('分块消息重组完成', ws.clientId, {
                    messageId,
                    totalSize: completeMessage.length,
                    totalChunks
                });
                // 解析重组后的消息
                return JSON.parse(completeMessage);
            }
            // 消息尚未完整
            return null;
        }
        catch (error) {
            logger.error('处理分块消息失败:', {
                error: error.message,
                clientId: ws.clientId,
                messageId: chunkMessage.messageId
            });
            // 清理错误的消息缓存
            if (chunkMessage.messageId) {
                this.chunkedMessages.delete(chunkMessage.messageId);
            }
            throw new AppError('分块消息处理失败', 400, ErrorTypes.VALIDATION_ERROR);
        }
    }
    /**
     * 清理过期的分块消息缓存
     */
    cleanupExpiredChunkedMessages() {
        const now = Date.now();
        const CHUNK_TIMEOUT = 2 * 60 * 1000; // 2分钟超时，更积极的清理
        let cleanedCount = 0;
        for (const [messageId, messageCache] of this.chunkedMessages.entries()) {
            if (now - messageCache.timestamp > CHUNK_TIMEOUT) {
                logger.warn('清理过期的分块消息', {
                    messageId,
                    clientId: messageCache.clientId,
                    receivedCount: messageCache.receivedCount,
                    totalChunks: messageCache.totalChunks,
                    ageMinutes: Math.round((now - messageCache.timestamp) / 60000)
                });
                this.chunkedMessages.delete(messageId);
                cleanedCount++;
            }
        }
        if (cleanedCount > 0) {
            logger.info(`清理了 ${cleanedCount} 个过期分块消息，当前缓存大小: ${this.chunkedMessages.size}`);
        }
    }
    /**
     * 清理过期的系统信息挂起请求
     */
    cleanupExpiredPendingRequests() {
        const now = Date.now();
        const TIMEOUT = 60 * 1000; // 60秒
        let cleaned = 0;
        for (const [messageId, entry] of this.pendingSystemInfoRequests.entries()) {
            if (now - entry.timestamp > TIMEOUT) {
                this.pendingSystemInfoRequests.delete(messageId);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            logger.warn(`清理了 ${cleaned} 个过期的系统信息挂起请求`);
        }
    }
    /**
     * 格式化字节大小
     */
    formatBytes(bytes) {
        if (bytes === 0)
            return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}
module.exports = WebSocketManager;
//# sourceMappingURL=websocketManager.js.map