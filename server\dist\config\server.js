/**
 * 服务端配置文件
 * 包含服务器、数据库、安全等相关配置
 */
const path = require('path');
const config = {
    // 服务器配置
    host: process.env.HOST || '0.0.0.0',
    port: parseInt(process.env.PORT) || 3000,
    // WebSocket配置
    websocket: {
        heartbeatInterval: 30000, // 30秒心跳间隔
        connectionTimeout: 60000 // 60秒连接超时
    },
    // CORS配置
    cors: {
        allowedOrigins: [
            'http://localhost:5173', // Vite开发服务器
            'http://localhost:5174', // Vite开发服务器备用端口
            'http://localhost:3000', // 生产环境
            'http://127.0.0.1:5173',
            'http://127.0.0.1:5174',
            'http://127.0.0.1:3000'
        ]
    },
    // JWT配置
    jwt: {
        secret: process.env.JWT_SECRET || 'winrat-super-secret-key-2024',
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
        issuer: 'winrat-server',
        audience: 'winrat-client'
    },
    // 加密配置
    encryption: {
        algorithm: 'xor', // 使用简单的异或加密
        enabled: true,
        // 预共享密钥（生产环境应使用环境变量）
        presharedKey: process.env.ENCRYPTION_KEY || 'winrat-encryption-key-2024-secure'
    },
    // 日志配置
    logging: {
        level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'warn' : 'info'),
        file: {
            enabled: true,
            path: path.join(__dirname, '../../logs'),
            maxSize: '10m',
            maxFiles: '7d'
        },
        console: {
            enabled: process.env.NODE_ENV !== 'production' || process.env.CONSOLE_LOG === 'true',
            colorize: process.env.NODE_ENV !== 'production'
        }
    },
    // 安全配置
    security: {
        // 密码加密轮次
        bcryptRounds: 12,
        // 最大登录尝试次数
        maxLoginAttempts: 5,
        // 账户锁定时间（分钟）
        lockoutDuration: 15,
        // 会话超时时间（分钟）
        sessionTimeout: 60,
        // 允许的命令执行超时时间（毫秒）
        commandTimeout: 30000
    },
    // 默认用户配置
    defaultUsers: [
        {
            username: 'admin',
            password: 'admin123', // 生产环境应修改
            role: 'administrator',
            permissions: ['*']
        }
    ],
    // Agent配置
    agent: {
        // Agent心跳超时时间
        heartbeatTimeout: 90000, // 90秒
        // Agent注册超时时间
        registerTimeout: 30000, // 30秒
        // 最大离线时间
        maxOfflineTime: 300000, // 5分钟
        // 支持的命令类型
        supportedCommands: [
            '*' // 允许所有命令
        ]
    },
    // 开发环境配置
    development: {
        // 启用详细日志
        verboseLogging: true,
        // 禁用某些安全检查
        skipAuthForHealthCheck: true,
        // 允许不安全的连接
        allowInsecureConnections: false,
        // 显示详细启动信息
        showStartupInfo: true
    },
    // 生产环境配置
    production: {
        // 隐藏错误详情
        hideErrorDetails: true,
        // 关闭详细日志
        verboseLogging: false,
        // 不显示详细启动信息
        showStartupInfo: false,
        // 强制安全检查
        skipAuthForHealthCheck: false
    }
};
// 根据环境变量覆盖配置
const env = process.env.NODE_ENV || 'development';
if (config[env]) {
    Object.assign(config, config[env]);
}
module.exports = config;
//# sourceMappingURL=server.js.map