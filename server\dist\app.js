// @ts-nocheck
/**
 * WinRAT服务端主应用
 * 负责启动Express服务器和WebSocket服务
 */
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const http = require('http');
const WebSocket = require('ws');
// 导入路由和中间件
const authRoutes = require('./routes/auth');
const agentRoutes = require('./routes/agents');
const { router: pluginsRoutes, initializePluginRegistry } = require('./routes/plugins');
const { authenticateToken } = require('./middleware/auth');
const { errorHandler } = require('./middleware/errorHandler');
const logger = require('./utils/logger');
const config = require('./config/server');
// 导入WebSocket处理器
const WebSocketManager = require('./services/websocketManager');
class WinRATServer {
    constructor() {
        this.app = express();
        this.server = null;
        this.wsManager = null;
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
    }
    /**
     * 设置Express中间件
     */
    setupMiddleware() {
        // 安全中间件
        this.app.use(helmet({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                }
            }
        }));
        // CORS配置
        this.app.use(cors({
            origin: config.cors.allowedOrigins,
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization']
        }));
        // 请求解析
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
        // 请求日志
        this.app.use((req, res, next) => {
            logger.info(`${req.method} ${req.path} - ${req.ip}`);
            next();
        });
    }
    /**
     * 设置API路由
     */
    setupRoutes() {
        // 健康检查
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                timestamp: new Date().toISOString(),
                version: require('../../package.json').version
            });
        });
        // API路由
        this.app.use('/api/auth', authRoutes);
        this.app.use('/api/agents', authenticateToken, agentRoutes);
        this.app.use('/api/plugins', authenticateToken, pluginsRoutes);
        // 404处理
        this.app.use('*', (req, res) => {
            res.status(404).json({
                success: false,
                message: '接口不存在',
                path: req.originalUrl
            });
        });
    }
    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        this.app.use(errorHandler);
    }
    /**
     * 启动服务器
     */
    async start() {
        try {
            // 创建HTTP服务器
            this.server = http.createServer(this.app);
            // 创建WebSocket服务器 - 最大性能配置
            const wss = new WebSocket.Server({
                server: this.server,
                path: '/ws',
                maxPayload: 256 * 1024 * 1024,
                perMessageDeflate: {
                    threshold: 1024,
                    concurrencyLimit: 20,
                    memLevel: 9,
                    level: 6,
                    serverMaxWindowBits: 15,
                    clientMaxWindowBits: 15,
                },
                backlog: 1024,
                maxConnections: 1000
            });
            // 初始化WebSocket管理器
            this.wsManager = new WebSocketManager(wss);
            // 设置WebSocket管理器到中间件
            const { setWebSocketManager } = require('./middleware/websocket');
            setWebSocketManager(this.wsManager);
            // 设置插件路由的WebSocket管理器
            pluginsRoutes.wsManager = this.wsManager;
            // 初始化插件注册表
            await initializePluginRegistry();
            // 启动服务器
            this.server.listen(config.port, config.host, () => {
                logger.info(`WinRAT服务端启动成功`);
                logger.info(`HTTP服务: http://${config.host}:${config.port}`);
                logger.info(`WebSocket服务: ws://${config.host}:${config.port}/ws`);
                logger.info(`环境: ${process.env.NODE_ENV || 'development'}`);
            });
            // 优雅关闭处理
            this.setupGracefulShutdown();
        }
        catch (error) {
            logger.error('服务器启动失败:', error);
            process.exit(1);
        }
    }
    /**
     * 设置优雅关闭
     */
    setupGracefulShutdown() {
        const shutdown = (signal) => {
            logger.info(`收到${signal}信号，开始优雅关闭...`);
            if (this.server) {
                this.server.close(() => {
                    logger.info('HTTP服务器已关闭');
                });
            }
            if (this.wsManager) {
                this.wsManager.shutdown();
                logger.info('WebSocket服务器已关闭');
            }
            setTimeout(() => {
                logger.info('强制退出');
                process.exit(0);
            }, 10000);
        };
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGINT', () => shutdown('SIGINT'));
    }
    /**
     * 获取WebSocket管理器实例
     */
    getWebSocketManager() {
        return this.wsManager;
    }
}
// 启动服务器
if (require.main === module) {
    const server = new WinRATServer();
    server.start().catch(error => {
        console.error('启动失败:', error);
        process.exit(1);
    });
}
module.exports = WinRATServer;
//# sourceMappingURL=app.js.map