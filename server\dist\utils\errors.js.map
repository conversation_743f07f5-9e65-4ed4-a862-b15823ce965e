{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/utils/errors.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,UAAU,GAAG;IACf,gBAAgB,EAAE,kBAAkB;IACpC,oBAAoB,EAAE,sBAAsB;IAC5C,mBAAmB,EAAE,qBAAqB;IAC1C,eAAe,EAAE,iBAAiB;IAClC,cAAc,EAAE,gBAAgB;IAChC,gBAAgB,EAAE,kBAAkB;IACpC,cAAc,EAAE,gBAAgB;IAChC,eAAe,EAAE,iBAAiB;IAClC,WAAW,EAAE,aAAa;IAC1B,aAAa,EAAE,eAAe;IAC9B,UAAU,EAAE,YAAY;IACxB,YAAY,EAAE,cAAc;CAC/B,CAAC;AAEF;;GAEG;AACH,MAAM,QAAS,SAAQ,KAAK;IACxB,YAAY,OAAO,EAAE,UAAU,GAAG,GAAG,EAAE,IAAI,GAAG,UAAU,CAAC,cAAc,EAAE,OAAO,GAAG,IAAI;QACnF,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAE1C,WAAW;QACX,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC1B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC5C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM;QACF,OAAO;YACH,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,KAAK,EAAE,IAAI,CAAC,KAAK;SACpB,CAAC;IACN,CAAC;IAED;;OAEG;IACH,cAAc;QACV,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAChB,KAAK,UAAU,CAAC,gBAAgB;gBAC5B,OAAO,UAAU,CAAC;YACtB,KAAK,UAAU,CAAC,oBAAoB;gBAChC,OAAO,QAAQ,CAAC;YACpB,KAAK,UAAU,CAAC,mBAAmB;gBAC/B,OAAO,MAAM,CAAC;YAClB,KAAK,UAAU,CAAC,eAAe;gBAC3B,OAAO,UAAU,CAAC;YACtB,KAAK,UAAU,CAAC,cAAc;gBAC1B,OAAO,MAAM,CAAC;YAClB,KAAK,UAAU,CAAC,gBAAgB;gBAC5B,OAAO,QAAQ,CAAC;YACpB,KAAK,UAAU,CAAC,eAAe;gBAC3B,OAAO,eAAe,CAAC;YAC3B,KAAK,UAAU,CAAC,WAAW;gBACvB,OAAO,WAAW,CAAC;YACvB,KAAK,UAAU,CAAC,aAAa;gBACzB,OAAO,QAAQ,CAAC;YACpB,KAAK,UAAU,CAAC,UAAU;gBACtB,OAAO,QAAQ,CAAC;YACpB,KAAK,UAAU,CAAC,YAAY;gBACxB,OAAO,QAAQ,CAAC;YACpB;gBACI,OAAO,SAAS,CAAC;QACzB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,aAAa;QACT,OAAO,IAAI,CAAC,UAAU,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,aAAa;QACT,OAAO,IAAI,CAAC,UAAU,IAAI,GAAG,CAAC;IAClC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,eAAgB,SAAQ,QAAQ;IAClC,YAAY,OAAO,EAAE,OAAO,GAAG,IAAI;QAC/B,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QAC1D,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAClC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,mBAAoB,SAAQ,QAAQ;IACtC,YAAY,OAAO,GAAG,QAAQ;QAC1B,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,oBAAoB,CAAC,CAAC;QACrD,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;IACtC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,kBAAmB,SAAQ,QAAQ;IACrC,YAAY,OAAO,GAAG,MAAM;QACxB,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,mBAAmB,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;IACrC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,aAAc,SAAQ,QAAQ;IAChC,YAAY,OAAO,GAAG,OAAO;QACzB,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAChC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,aAAc,SAAQ,QAAQ;IAChC,YAAY,OAAO,GAAG,MAAM;QACxB,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,CAAC,cAAc,CAAC,CAAC;QAC/C,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAChC,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,UAAW,SAAQ,QAAQ;IAC7B,YAAY,OAAO,EAAE,UAAU,GAAG,GAAG;QACjC,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;QACnD,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;IAC7B,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,WAAY,SAAQ,QAAQ;IAC9B,YAAY,OAAO,EAAE,UAAU,GAAG,GAAG;QACjC,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;IAC9B,CAAC;CACJ;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,KAAK,EAAE,YAAY,GAAG,KAAK;IACpD,MAAM,QAAQ,GAAG;QACb,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACH,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,UAAU,CAAC,cAAc;YAC7C,SAAS,EAAE,KAAK,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACzD;KACJ,CAAC;IAEF,SAAS;IACT,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IAC3C,CAAC;IAED,eAAe;IACf,IAAI,YAAY,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;QAC9B,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;IACvC,CAAC;IAED,OAAO,QAAQ,CAAC;AACpB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,GAAG;IACf;;OAEG;IACH,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE;QACd,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;YACtB,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACH,qBAAqB,EAAE,CAAC,MAAM,EAAE,EAAE;QAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACxB,OAAO,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACtB,KAAK,EAAE,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,KAAK;gBAC5B,OAAO,EAAE,GAAG,CAAC,OAAO;gBACpB,KAAK,EAAE,GAAG,CAAC,KAAK;aACnB,CAAC,CAAC,CAAC;QACR,CAAC;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,kBAAkB,EAAE,CAAC,KAAK,EAAE,EAAE;QAC1B,OAAO,KAAK,YAAY,QAAQ,CAAC;IACrC,CAAC;CACJ,CAAC;AAEF,MAAM,CAAC,OAAO,GAAG;IACb,UAAU;IACV,QAAQ;IACR,eAAe;IACf,mBAAmB;IACnB,kBAAkB;IAClB,aAAa;IACb,aAAa;IACb,UAAU;IACV,WAAW;IACX,mBAAmB;IACnB,UAAU;CACb,CAAC"}