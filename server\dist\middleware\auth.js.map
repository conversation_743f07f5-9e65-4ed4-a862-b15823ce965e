{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,MAAM,GAAG,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AACpC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC5D,MAAM,MAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAC3C,MAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAE1C;;GAEG;AACH,MAAM,iBAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;IACzC,IAAI,CAAC;QACD,WAAW;QACX,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAChD,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe;QAErE,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,IAAI,QAAQ,CACd,cAAc,EACd,GAAG,EACH,UAAU,CAAC,oBAAoB,CAClC,CAAC;QACN,CAAC;QAED,OAAO;QACP,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE;YACjD,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM;YACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ;SAChC,CAAC,CAAC;QAEH,WAAW;QACX,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC1C,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;YACnC,MAAM,IAAI,QAAQ,CACd,SAAS,EACT,GAAG,EACH,UAAU,CAAC,oBAAoB,CAClC,CAAC;QACN,CAAC;QAED,eAAe;QACf,GAAG,CAAC,IAAI,GAAG;YACP,EAAE,EAAE,OAAO,CAAC,MAAM;YAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;YACtC,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,GAAG,EAAE,OAAO,CAAC,GAAG;SACnB,CAAC;QAEF,WAAW;QACX,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE;YACnB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;YACnB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;YAC3B,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;SACnC,CAAC,CAAC;QAEH,IAAI,EAAE,CAAC;IACX,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,IAAI,KAAK,YAAY,GAAG,CAAC,iBAAiB,EAAE,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE;gBAC1B,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;gBAChC,KAAK,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,IAAI,QAAQ,CACpB,SAAS,EACT,GAAG,EACH,UAAU,CAAC,oBAAoB,CAClC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,KAAK,YAAY,GAAG,CAAC,iBAAiB,EAAE,CAAC;YACzC,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE;gBAC1B,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;aACnC,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,IAAI,QAAQ,CACpB,SAAS,EACT,GAAG,EACH,UAAU,CAAC,oBAAoB,CAClC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,CAAC;IAChB,CAAC;AACL,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,iBAAiB,GAAG,CAAC,UAAU,EAAE,EAAE;IACrC,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;QACtB,IAAI,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACZ,MAAM,IAAI,QAAQ,CACd,OAAO,EACP,GAAG,EACH,UAAU,CAAC,oBAAoB,CAClC,CAAC;YACN,CAAC;YAED,YAAY;YACZ,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;gBACpC,OAAO,IAAI,EAAE,CAAC;YAClB,CAAC;YAED,cAAc;YACd,MAAM,aAAa,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAC3C,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAEvD,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE;oBACvB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;oBACnB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;oBAC3B,kBAAkB,EAAE,UAAU;oBAC9B,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC,WAAW;oBACrC,EAAE,EAAE,GAAG,CAAC,EAAE;iBACb,CAAC,CAAC;gBAEH,MAAM,IAAI,QAAQ,CACd,MAAM,EACN,GAAG,EACH,UAAU,CAAC,mBAAmB,CACjC,CAAC;YACN,CAAC;YAED,IAAI,EAAE,CAAC;QACX,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC;IACL,CAAC,CAAC;AACN,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,EAAE;IAC1B,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAEzD,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;QACtB,IAAI,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACZ,MAAM,IAAI,QAAQ,CACd,OAAO,EACP,GAAG,EACH,UAAU,CAAC,oBAAoB,CAClC,CAAC;YACN,CAAC;YAED,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE;oBACzB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;oBACnB,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;oBAC3B,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI;oBACvB,aAAa,EAAE,SAAS;oBACxB,EAAE,EAAE,GAAG,CAAC,EAAE;iBACb,CAAC,CAAC;gBAEH,MAAM,IAAI,QAAQ,CACd,QAAQ,EACR,GAAG,EACH,UAAU,CAAC,mBAAmB,CACjC,CAAC;YACN,CAAC;YAED,IAAI,EAAE,CAAC;QACX,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC;IACL,CAAC,CAAC;AACN,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;IACpC,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAChD,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD,IAAI,CAAC,KAAK,EAAE,CAAC;QACT,OAAO,IAAI,EAAE,CAAC;IAClB,CAAC;IAED,IAAI,CAAC;QACD,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE;YACjD,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM;YACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ;SAChC,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,GAAG;YACP,EAAE,EAAE,OAAO,CAAC,MAAM;YAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;SACzC,CAAC;IACN,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,gBAAgB;QAChB,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,IAAI,EAAE,CAAC;AACX,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,aAAa,GAAG,CAAC,IAAI,EAAE,EAAE;IAC3B,MAAM,OAAO,GAAG;QACZ,MAAM,EAAE,IAAI,CAAC,EAAE;QACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;QACvB,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;KACtC,CAAC;IAEF,MAAM,OAAO,GAAG;QACZ,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,SAAS;QAC/B,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM;QACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ;KAChC,CAAC;IAEF,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,GAAG,CAAC,KAAK,EAAE,EAAE;IAC1B,IAAI,CAAC;QACD,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE;YACxC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM;YACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ;SAChC,CAAC,CAAC;IACP,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,MAAM,IAAI,QAAQ,CACd,SAAS,EACT,GAAG,EACH,UAAU,CAAC,oBAAoB,CAClC,CAAC;IACN,CAAC;AACL,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,EAAE;IAC3B,IAAI,CAAC;QACD,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE;YACjD,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM;YACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ;YAC7B,gBAAgB,EAAE,IAAI,CAAC,SAAS;SACnC,CAAC,CAAC;QAEH,gBAAgB;QAChB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC1C,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,KAAK;QAE7C,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,aAAa,EAAE,CAAC;YACpC,MAAM,IAAI,QAAQ,CACd,eAAe,EACf,GAAG,EACH,UAAU,CAAC,oBAAoB,CAClC,CAAC;QACN,CAAC;QAED,QAAQ;QACR,OAAO,aAAa,CAAC;YACjB,EAAE,EAAE,OAAO,CAAC,MAAM;YAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,WAAW,EAAE,OAAO,CAAC,WAAW;SACnC,CAAC,CAAC;IACP,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;YAC5B,MAAM,KAAK,CAAC;QAChB,CAAC;QACD,MAAM,IAAI,QAAQ,CACd,QAAQ,EACR,GAAG,EACH,UAAU,CAAC,oBAAoB,CAClC,CAAC;IACN,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,OAAO,GAAG;IACb,iBAAiB;IACjB,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,aAAa;IACb,WAAW;IACX,YAAY;CACf,CAAC"}