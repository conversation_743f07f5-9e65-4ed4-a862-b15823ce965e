/**
 * 错误类型枚举
 */
const ErrorTypes = {
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
    AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
    NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
    CONFLICT_ERROR: 'CONFLICT_ERROR',
    RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
    INTERNAL_ERROR: 'INTERNAL_ERROR',
    WEBSOCKET_ERROR: 'WEBSOCKET_ERROR',
    AGENT_ERROR: 'AGENT_ERROR',
    NETWORK_ERROR: 'NETWORK_ERROR',
    FILE_ERROR: 'FILE_ERROR',
    PLUGIN_ERROR: 'PLUGIN_ERROR'
};
/**
 * 自定义应用错误类
 */
class AppError extends Error {
    constructor(message, statusCode = 500, type = ErrorTypes.INTERNAL_ERROR, details = null) {
        super(message);
        this.name = 'AppError';
        this.statusCode = statusCode;
        this.type = type;
        this.details = details;
        this.timestamp = new Date().toISOString();
        // 确保堆栈跟踪正确
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, AppError);
        }
    }
    /**
     * 转换为JSON格式
     */
    toJSON() {
        return {
            name: this.name,
            message: this.message,
            statusCode: this.statusCode,
            type: this.type,
            details: this.details,
            timestamp: this.timestamp,
            stack: this.stack
        };
    }
    /**
     * 获取用户友好的错误消息
     */
    getUserMessage() {
        switch (this.type) {
            case ErrorTypes.VALIDATION_ERROR:
                return '输入数据验证失败';
            case ErrorTypes.AUTHENTICATION_ERROR:
                return '身份验证失败';
            case ErrorTypes.AUTHORIZATION_ERROR:
                return '权限不足';
            case ErrorTypes.NOT_FOUND_ERROR:
                return '请求的资源不存在';
            case ErrorTypes.CONFLICT_ERROR:
                return '资源冲突';
            case ErrorTypes.RATE_LIMIT_ERROR:
                return '请求过于频繁';
            case ErrorTypes.WEBSOCKET_ERROR:
                return 'WebSocket连接错误';
            case ErrorTypes.AGENT_ERROR:
                return 'Agent操作失败';
            case ErrorTypes.NETWORK_ERROR:
                return '网络连接错误';
            case ErrorTypes.FILE_ERROR:
                return '文件操作失败';
            case ErrorTypes.PLUGIN_ERROR:
                return '插件操作失败';
            default:
                return '服务器内部错误';
        }
    }
    /**
     * 判断是否为客户端错误
     */
    isClientError() {
        return this.statusCode >= 400 && this.statusCode < 500;
    }
    /**
     * 判断是否为服务器错误
     */
    isServerError() {
        return this.statusCode >= 500;
    }
}
/**
 * 验证错误
 */
class ValidationError extends AppError {
    constructor(message, details = null) {
        super(message, 400, ErrorTypes.VALIDATION_ERROR, details);
        this.name = 'ValidationError';
    }
}
/**
 * 认证错误
 */
class AuthenticationError extends AppError {
    constructor(message = '身份验证失败') {
        super(message, 401, ErrorTypes.AUTHENTICATION_ERROR);
        this.name = 'AuthenticationError';
    }
}
/**
 * 授权错误
 */
class AuthorizationError extends AppError {
    constructor(message = '权限不足') {
        super(message, 403, ErrorTypes.AUTHORIZATION_ERROR);
        this.name = 'AuthorizationError';
    }
}
/**
 * 资源未找到错误
 */
class NotFoundError extends AppError {
    constructor(message = '资源不存在') {
        super(message, 404, ErrorTypes.NOT_FOUND_ERROR);
        this.name = 'NotFoundError';
    }
}
/**
 * 资源冲突错误
 */
class ConflictError extends AppError {
    constructor(message = '资源冲突') {
        super(message, 409, ErrorTypes.CONFLICT_ERROR);
        this.name = 'ConflictError';
    }
}
/**
 * Agent错误
 */
class AgentError extends AppError {
    constructor(message, statusCode = 503) {
        super(message, statusCode, ErrorTypes.AGENT_ERROR);
        this.name = 'AgentError';
    }
}
/**
 * 插件错误
 */
class PluginError extends AppError {
    constructor(message, statusCode = 500) {
        super(message, statusCode, ErrorTypes.PLUGIN_ERROR);
        this.name = 'PluginError';
    }
}
/**
 * 创建标准化的错误响应
 */
function createErrorResponse(error, includeStack = false) {
    const response = {
        success: false,
        error: {
            message: error.message,
            type: error.type || ErrorTypes.INTERNAL_ERROR,
            timestamp: error.timestamp || new Date().toISOString()
        }
    };
    // 添加详细信息
    if (error.details) {
        response.error.details = error.details;
    }
    // 在开发环境中包含堆栈跟踪
    if (includeStack && error.stack) {
        response.error.stack = error.stack;
    }
    return response;
}
/**
 * 错误处理工具函数
 */
const ErrorUtils = {
    /**
     * 包装异步函数以捕获错误
     */
    wrapAsync: (fn) => {
        return (req, res, next) => {
            Promise.resolve(fn(req, res, next)).catch(next);
        };
    },
    /**
     * 格式化验证错误
     */
    formatValidationError: (errors) => {
        if (Array.isArray(errors)) {
            return errors.map(err => ({
                field: err.path || err.field,
                message: err.message,
                value: err.value
            }));
        }
        return errors;
    },
    /**
     * 判断错误是否为操作错误（已知错误）
     */
    isOperationalError: (error) => {
        return error instanceof AppError;
    }
};
module.exports = {
    ErrorTypes,
    AppError,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    NotFoundError,
    ConflictError,
    AgentError,
    PluginError,
    createErrorResponse,
    ErrorUtils
};
//# sourceMappingURL=errors.js.map