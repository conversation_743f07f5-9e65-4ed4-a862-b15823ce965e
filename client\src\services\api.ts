/**
 * WinRAT 客户端 API 服务
 * 负责与服务端的HTTP API通信
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  LoginRequest,
  LoginResponse,
  Agent,
  CommandRequest,
  CommandResult,
  SystemInfo,
  ApiResponse,
  User
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: 'http://localhost:3000/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器 - 添加认证token
    this.api.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器 - 处理错误
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401 && window.location.pathname !== '/login') {
          // Token过期，清除本地存储
          this.clearToken();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );

    // 从localStorage恢复token
    this.loadToken();
  }

  /**
   * 设置认证token
   */
  setToken(token: string): void {
    this.token = token;
    localStorage.setItem('winrat_token', token);
  }

  /**
   * 清除认证token
   */
  clearToken(): void {
    this.token = null;
    localStorage.removeItem('winrat_token');
  }

  /**
   * 从localStorage加载token
   */
  private loadToken(): void {
    const token = localStorage.getItem('winrat_token');
    if (token) {
      this.token = token;
    }
  }

  /**
   * 检查是否已认证
   */
  isAuthenticated(): boolean {
    return !!this.token;
  }

  // ==================== 认证相关API ====================

  /**
   * 用户登录
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.api.post<ApiResponse<{token: string, user: User, expiresIn: string}>>('/auth/login', credentials);

    if (response.data.success && response.data.data) {
      const { token, user, expiresIn } = response.data.data;

      if (token) {
        this.setToken(token);
      }

      return {
        success: true,
        token,
        user,
        expiresIn: expiresIn
      };
    }

    throw new Error('登录失败：无效的响应');
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } finally {
      this.clearToken();
    }
  }

  /**
   * 验证token
   */
  async verifyToken(): Promise<boolean> {
    try {
      await this.api.get('/auth/verify');
      return true;
    } catch {
      this.clearToken();
      return false;
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const response = await this.api.get<ApiResponse<User>>('/auth/me');
      return response.data.data || null;
    } catch {
      return null;
    }
  }

  // ==================== Agent相关API ====================
  // 旧的按需详情API未使用，已移除；统一由WS维护在线信息
  /**
   * 卸载Agent
   */
  async uninstallAgent(agentId: string): Promise<{uninstallId: string, agentId: string, hostname: string, message: string}> {
    const response = await this.api.post<ApiResponse<{uninstallId: string, agentId: string, hostname: string, message: string}>>(`/agents/${agentId}/uninstall`, {
      confirm: true
    });
    return response.data.data!;
  }

  // 已废弃的命令历史接口移除，命令执行记录在前端内存中维护

  // ==================== 插件相关API ====================

  /**
   * 获取插件列表
   */
  async getPlugins(): Promise<any> {
    const response = await this.api.get('/plugins');
    return response.data;
  }

  /**
   * 上传插件
   */
  async uploadPlugin(formData: FormData): Promise<any> {
    const response = await this.api.post('/plugins/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * 删除插件
   */
  async deletePlugin(pluginId: string): Promise<any> {
    const response = await this.api.delete(`/plugins/${pluginId}`);
    return response.data;
  }

  /**
   * 获取插件详情
   */
  async getPluginDetails(pluginId: string): Promise<any> {
    const response = await this.api.get(`/plugins/${pluginId}`);
    return response.data;
  }

  /**
   * 部署插件到Agent
   */
  async deployPlugin(pluginId: string, agentId: string): Promise<any> {
    const response = await this.api.post(`/plugins/${pluginId}/download-to-agent`, {
      agentId: agentId
    });
    return response.data;
  }

  /**
   * 执行插件命令
   */
  async executePluginCommand(agentId: string, pluginName: string, command: string, params: any = {}): Promise<any> {
    const response = await this.api.post('/plugins/execute', {
      agentId: agentId,
      pluginName: pluginName,
      command: command,
      params: params
    });
    return response.data;
  }

  // TODO: 轻量级插件状态检查API - 等服务器端实现后添加

  // ==================== 健康检查 ====================

  /**
   * 服务器健康检查
   */
  async healthCheck(): Promise<any> {
    const response = await this.api.get('/health');
    return response.data;
  }
}

// 导出单例实例
export const apiService = new ApiService();
export default apiService;
