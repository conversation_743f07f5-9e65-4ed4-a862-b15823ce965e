// @ts-nocheck
/**
 * Agent管理路由
 * 处理Agent相关的API请求
 */
const express = require('express');
const { asyncHandler } = require('../middleware/errorHandler');
const { AppError, ErrorTypes } = require('../utils/errors');
const { requirePermission } = require('../middleware/auth');
const { getConnectedAgents, validateAgent } = require('../middleware/websocket');
const logger = require('../utils/logger');
const router = express.Router();
// 已移除：GET /api/agents 列表接口（前端完全依赖WS推送）
// 已移除：GET /api/agents/:id 详情接口（不再通过REST提供详细信息）
/**
 * DELETE /api/agents/:id
 * 断开并移除指定Agent
 */
router.delete('/:id', requirePermission('agent:delete'), asyncHandler(async (req, res) => {
    const { id } = req.params;
    const agent = validateAgent(id);
    if (!agent) {
        throw new AppError(`Agent ${id} 不存在`, 404, ErrorTypes.NOT_FOUND_ERROR);
    }
    // 断开WebSocket连接
    const wsManager = require('../middleware/websocket').getWebSocketManager();
    const wsAgent = wsManager.agents.get(id);
    if (wsAgent) {
        wsAgent.close(1000, '管理员断开连接');
        wsManager.agents.delete(id);
    }
    logger.logAgent(id, 'Agent被管理员移除', {
        username: req.user.username,
        hostname: agent.hostname
    });
    res.json({
        success: true,
        message: `Agent ${agent.hostname} 已断开连接`
    });
}));
// 已移除：GET /api/agents/:id/system-info（系统信息经由WS点对点返回）
/**
 * GET /api/agents/stats
 * 获取Agent统计信息
 */
router.get('/stats/summary', requirePermission('agent:read'), asyncHandler(async (req, res) => {
    const agents = getConnectedAgents();
    const stats = {
        total: agents.length,
        online: agents.filter(agent => agent.status === 'online').length,
        offline: agents.filter(agent => agent.status === 'offline').length,
        byOS: {},
        byVersion: {},
        recentConnections: agents
            .filter(agent => agent.registeredAt)
            .sort((a, b) => new Date(b.registeredAt) - new Date(a.registeredAt))
            .slice(0, 10)
            .map(agent => ({
            id: agent.id,
            hostname: agent.hostname,
            os: agent.os,
            registeredAt: agent.registeredAt
        }))
    };
    // 按操作系统分组
    agents.forEach(agent => {
        const os = agent.os || 'Unknown';
        stats.byOS[os] = (stats.byOS[os] || 0) + 1;
    });
    // 按版本分组
    agents.forEach(agent => {
        const version = agent.version || 'Unknown';
        stats.byVersion[version] = (stats.byVersion[version] || 0) + 1;
    });
    logger.info('获取Agent统计信息', {
        username: req.user.username,
        total: stats.total
    });
    res.json({
        success: true,
        data: stats
    });
}));
// 已移除：POST /api/agents/broadcast（不再通过REST广播，保留WS能力）
/**
 * POST /api/agents/:id/uninstall
 * 向指定Agent发送终止指令
 */
router.post('/:id/uninstall', requirePermission('agent:delete'), asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { confirm = false } = req.body;
    // 安全检查：需要明确确认
    if (!confirm) {
        throw new AppError('终止操作需要明确确认，请设置 confirm: true', 400, ErrorTypes.VALIDATION_ERROR);
    }
    const wsManager = require('../middleware/websocket').getWebSocketManager();
    const wsAgent = wsManager.agents.get(id);
    if (!wsAgent) {
        throw new AppError(`Agent ${id} 不存在`, 404, ErrorTypes.NOT_FOUND_ERROR);
    }
    const agent = wsAgent.agentInfo;
    if (!agent || agent.status !== 'online') {
        throw new AppError(`Agent ${agent?.hostname || id} 当前离线，无法执行终止操作`, 503, ErrorTypes.AGENT_ERROR);
    }
    // 发送终止指令
    const uninstallId = `terminate_${Date.now()}`;
    wsManager.sendMessage(wsAgent, {
        type: 'uninstall',
        id: uninstallId,
        data: {
            timestamp: new Date().toISOString(),
            requestedBy: req.user.username
        }
    });
    logger.logAgent(id, 'Agent终止指令发送', {
        username: req.user.username,
        hostname: agent.hostname,
        uninstallId: uninstallId
    });
    res.json({
        success: true,
        message: `终止指令已发送到 ${agent.hostname}`,
        data: {
            uninstallId: uninstallId,
            agentId: id,
            hostname: agent.hostname,
            timestamp: new Date().toISOString()
        }
    });
}));
module.exports = router;
//# sourceMappingURL=agents.js.map