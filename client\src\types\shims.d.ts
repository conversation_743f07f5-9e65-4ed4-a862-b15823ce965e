// Declarations for JS/JSX modules used in TS/TSX files
// Keep only components still in JS/JSX

declare module '../components/FileManager/FileManager.tsx' {
  const FileManager: any;
  export default FileManager;
}

declare module 'client/src/components/FileManager/FileManager.tsx' {
  const FileManager: any;
  export default FileManager;
}

// Services ambient declarations (treat as any)
declare module '../services/fileManagerService' {
  export const fileManagerService: any;
}

declare module '../services/systemCommandService' {
  const systemCommandService: any;
  export default systemCommandService;
}

declare module '../services/pluginStateManager' {
  const pluginStateManager: any;
  export default pluginStateManager;
} 

declare module '*.tsx' {
  const ReactComponent: any;
  export default ReactComponent;
} 