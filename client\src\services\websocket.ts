/**
 * WinRAT 客户端 WebSocket 服务
 * 负责与服务端的WebSocket通信
 */

import { WebSocketMessage, Agent, CommandResult } from '../types';

type MessageHandler = (message: WebSocketMessage) => void;
type ConnectionHandler = (connected: boolean) => void;
type ErrorHandler = (error: Event) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private token: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;
  private isConnecting = false;
  private shouldReconnect = true;

  // 事件处理器
  private messageHandlers: Map<string, MessageHandler[]> = new Map();
  private connectionHandlers: ConnectionHandler[] = [];
  private errorHandlers: ErrorHandler[] = [];

  // 分块消息缓存
  private chunkedMessages: Map<string, {
    chunks: string[];
    receivedCount: number;
    totalChunks: number;
    timestamp: number;
  }> = new Map();

  constructor() {
    this.url = `ws://${window.location.hostname}:3000/ws`;

    // 定期清理过期的分块消息缓存（每分钟）
    setInterval(() => {
      this.cleanupExpiredChunkedMessages();
    }, 60000);
  }

  /**
   * 设置认证token
   */
  setToken(token: string): void {
    this.token = token;
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting) {
        resolve();
        return;
      }

      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        // 如果已连接但未认证，重新认证
        if (this.token) {
          this.authenticate();
        }
        resolve();
        return;
      }

      this.isConnecting = true;
      this.shouldReconnect = true;

      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {

          this.isConnecting = false;
          this.reconnectAttempts = 0;
          
          // 发送认证消息
          if (this.token) {
            this.authenticate();
          }
          
          this.notifyConnectionHandlers(true);
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const rawMessage = JSON.parse(event.data);

            // 检查是否是分块消息
            if (rawMessage.isChunked) {
              try {
                const completeMessage = this.handleChunkedMessage(rawMessage);
                if (completeMessage) {
                  this.handleMessage(completeMessage);
                }
                // 如果消息未完整，等待更多分块
              } catch (chunkError) {
                console.error('处理分块消息失败:', chunkError);
                // 清理可能损坏的分块缓存
                if (rawMessage.messageId) {
                  this.chunkedMessages.delete(rawMessage.messageId);
                }
              }
            } else {
              this.handleMessage(rawMessage);
            }
          } catch (error) {
            console.error('WebSocket消息解析失败:', error);
          }
        };

        this.ws.onclose = () => {
          this.isConnecting = false;
          this.notifyConnectionHandlers(false);
          
          if (this.shouldReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket错误:', error);
          this.isConnecting = false;
          this.notifyErrorHandlers(error);
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * 断开WebSocket连接
   */
  disconnect(): void {
    this.shouldReconnect = false;
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  /**
   * 发送认证消息
   */
  private authenticate(): void {
    if (this.token) {
      this.sendMessage({
        type: 'client_auth',
        data: { token: this.token }
      });
    }
  }

  // 取消 requestAgentList：完全依赖服务端推送

  /**
   * 发送消息
   */
  sendMessage(message: Partial<WebSocketMessage>): string | undefined {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const fullMessage: WebSocketMessage = {
        id: this.generateId(),
        timestamp: new Date().toISOString(),
        type: message.type || 'unknown',
        data: message.data || {},
        ...message
      };

      this.ws.send(JSON.stringify(fullMessage));
      return fullMessage.id;
    } else {
      console.warn('WebSocket未连接，无法发送消息');
      return undefined;
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage): void {
    // 处理认证成功消息 - 不在这里请求Agent列表，由AgentContext统一处理
    if (message.type === 'auth_success') {
      // 认证成功，但不在这里请求Agent列表
      // Agent列表请求由AgentContext统一管理
    }

    const handlers = this.messageHandlers.get(message.type) || [];


    handlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('消息处理器错误:', error);
      }
    });

    // 处理通用消息
    const allHandlers = this.messageHandlers.get('*') || [];
    allHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('通用消息处理器错误:', error);
      }
    });
  }

  /**
   * 添加消息处理器
   */
  onMessage(type: string, handler: MessageHandler): () => void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type)!.push(handler);

    // 返回取消订阅函数
    return () => {
      const handlers = this.messageHandlers.get(type);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  /**
   * 移除消息处理器
   */
  offMessage(type: string, handler: MessageHandler): void {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * 添加连接状态处理器
   */
  onConnection(handler: ConnectionHandler): () => void {
    this.connectionHandlers.push(handler);
    
    // 返回取消订阅函数
    return () => {
      const index = this.connectionHandlers.indexOf(handler);
      if (index > -1) {
        this.connectionHandlers.splice(index, 1);
      }
    };
  }

  /**
   * 添加错误处理器
   */
  onError(handler: ErrorHandler): () => void {
    this.errorHandlers.push(handler);
    
    // 返回取消订阅函数
    return () => {
      const index = this.errorHandlers.indexOf(handler);
      if (index > -1) {
        this.errorHandlers.splice(index, 1);
      }
    };
  }

  /**
   * 通知连接状态处理器
   */
  private notifyConnectionHandlers(connected: boolean): void {
    this.connectionHandlers.forEach(handler => {
      try {
        handler(connected);
      } catch (error) {
        console.error('连接状态处理器错误:', error);
      }
    });
  }

  /**
   * 通知错误处理器
   */
  private notifyErrorHandlers(error: Event): void {
    this.errorHandlers.forEach(handler => {
      try {
        handler(error);
      } catch (error) {
        console.error('错误处理器错误:', error);
      }
    });
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    this.reconnectAttempts++;

    
    setTimeout(() => {
      if (this.shouldReconnect) {
        this.connect().catch(error => {
          console.error('重连失败:', error);
        });
      }
    }, this.reconnectInterval);
  }

  /**
   * 生成消息ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  /**
   * 处理分块消息
   */
  private handleChunkedMessage(chunkMessage: any): WebSocketMessage | null {
    try {
      const { messageId, chunkIndex, totalChunks, data } = chunkMessage;

      console.log('收到分块消息:', {
        messageId,
        chunkIndex,
        totalChunks,
        chunkSize: data.length
      });

      // 初始化消息缓存
      if (!this.chunkedMessages.has(messageId)) {
        this.chunkedMessages.set(messageId, {
          chunks: new Array(totalChunks),
          receivedCount: 0,
          totalChunks,
          timestamp: Date.now()
        });
      }

      const messageCache = this.chunkedMessages.get(messageId)!;

      // 验证分块信息
      if (messageCache.totalChunks !== totalChunks) {
        throw new Error(`分块总数不匹配: 期望 ${messageCache.totalChunks}, 收到 ${totalChunks}`);
      }

      if (chunkIndex >= totalChunks) {
        throw new Error(`分块索引超出范围: ${chunkIndex} >= ${totalChunks}`);
      }

      // 存储分块数据
      if (!messageCache.chunks[chunkIndex]) {
        messageCache.chunks[chunkIndex] = data;
        messageCache.receivedCount++;

        console.log('分块已存储:', {
          messageId,
          chunkIndex,
          receivedCount: messageCache.receivedCount,
          totalChunks
        });
      }

      // 检查是否收到所有分块
      if (messageCache.receivedCount === totalChunks) {
        // 重组完整消息
        const completeMessage = messageCache.chunks.join('');

        // 清理缓存
        this.chunkedMessages.delete(messageId);

        console.log('分块消息重组完成:', {
          messageId,
          totalSize: completeMessage.length,
          totalChunks
        });

        // 解析重组后的消息
        return JSON.parse(completeMessage);
      }

      // 消息尚未完整
      return null;

    } catch (error) {
      console.error('处理分块消息失败:', error);

      // 清理错误的消息缓存
      if (chunkMessage.messageId) {
        this.chunkedMessages.delete(chunkMessage.messageId);
      }

      return null;
    }
  }

  /**
   * 清理过期的分块消息缓存
   */
  private cleanupExpiredChunkedMessages(): void {
    const now = Date.now();
    const CHUNK_TIMEOUT = 5 * 60 * 1000; // 5分钟超时

    for (const [messageId, messageCache] of this.chunkedMessages.entries()) {
      if (now - messageCache.timestamp > CHUNK_TIMEOUT) {
        console.warn('清理过期的分块消息:', {
          messageId,
          receivedCount: messageCache.receivedCount,
          totalChunks: messageCache.totalChunks
        });
        this.chunkedMessages.delete(messageId);
      }
    }
  }
}

// 导出单例实例
export const wsService = new WebSocketService();
export default wsService;
