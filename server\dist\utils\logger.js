/**
 * 日志工具模块
 * 提供统一的日志记录功能
 */
const fs = require('fs');
const path = require('path');
const config = require('../config/server');
class Logger {
    constructor() {
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3
        };
        this.colors = {
            error: '\x1b[31m', // 红色
            warn: '\x1b[33m', // 黄色
            info: '\x1b[36m', // 青色
            debug: '\x1b[37m', // 白色
            reset: '\x1b[0m' // 重置
        };
        this.currentLevel = this.levels[config.logging.level] || this.levels.info;
        this.ensureLogDirectory();
    }
    /**
     * 确保日志目录存在
     */
    ensureLogDirectory() {
        if (config.logging.file.enabled) {
            const logDir = config.logging.file.path;
            if (!fs.existsSync(logDir)) {
                fs.mkdirSync(logDir, { recursive: true });
            }
        }
    }
    /**
     * 格式化时间戳
     */
    formatTimestamp() {
        const now = new Date();
        return now.toISOString();
    }
    /**
     * 格式化日志消息
     */
    formatMessage(level, message, meta = {}) {
        const timestamp = this.formatTimestamp();
        const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
        return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}`;
    }
    /**
     * 写入日志文件
     */
    writeToFile(level, formattedMessage) {
        if (!config.logging.file.enabled)
            return;
        const logFile = path.join(config.logging.file.path, `winrat-${new Date().toISOString().split('T')[0]}.log`);
        try {
            fs.appendFileSync(logFile, formattedMessage + '\n');
        }
        catch (error) {
            console.error('写入日志文件失败:', error);
        }
    }
    /**
     * 输出到控制台
     */
    writeToConsole(level, message, meta = {}) {
        if (!config.logging.console.enabled)
            return;
        const timestamp = this.formatTimestamp();
        const color = config.logging.console.colorize ? this.colors[level] : '';
        const reset = config.logging.console.colorize ? this.colors.reset : '';
        const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta, null, 2)}` : '';
        console.log(`${color}[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}${reset}`);
    }
    /**
     * 通用日志方法
     */
    log(level, message, meta = {}) {
        const levelValue = this.levels[level];
        if (levelValue === undefined || levelValue > this.currentLevel) {
            return;
        }
        const formattedMessage = this.formatMessage(level, message, meta);
        // 写入文件
        this.writeToFile(level, formattedMessage);
        // 输出到控制台
        this.writeToConsole(level, message, meta);
    }
    /**
     * 错误日志
     */
    error(message, meta = {}) {
        // 如果message是Error对象，提取堆栈信息
        if (message instanceof Error) {
            meta.stack = message.stack;
            message = message.message;
        }
        this.log('error', message, meta);
    }
    /**
     * 警告日志
     */
    warn(message, meta = {}) {
        this.log('warn', message, meta);
    }
    /**
     * 信息日志
     */
    info(message, meta = {}) {
        this.log('info', message, meta);
    }
    /**
     * 调试日志
     */
    debug(message, meta = {}) {
        this.log('debug', message, meta);
    }
    /**
     * 记录HTTP请求
     */
    logRequest(req, res, duration) {
        const meta = {
            method: req.method,
            url: req.url,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            statusCode: res.statusCode,
            duration: `${duration}ms`
        };
        if (res.statusCode >= 400) {
            this.warn(`HTTP请求失败: ${req.method} ${req.url}`, meta);
        }
        else {
            this.info(`HTTP请求: ${req.method} ${req.url}`, meta);
        }
    }
    /**
     * 记录WebSocket事件
     */
    logWebSocket(event, clientId, data = {}) {
        const meta = {
            event,
            clientId,
            ...data
        };
        this.info(`WebSocket事件: ${event}`, meta);
    }
    /**
     * 记录安全事件
     */
    logSecurity(event, details = {}) {
        const meta = {
            event,
            timestamp: this.formatTimestamp(),
            ...details
        };
        this.warn(`安全事件: ${event}`, meta);
    }
    /**
     * 记录Agent事件
     */
    logAgent(agentId, event, data = {}) {
        const meta = {
            agentId,
            event,
            ...data
        };
        this.info(`Agent事件: ${event}`, meta);
    }
    /**
     * 清理旧日志文件
     */
    cleanupOldLogs() {
        if (!config.logging.file.enabled)
            return;
        const logDir = config.logging.file.path;
        const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
        const now = Date.now();
        try {
            const files = fs.readdirSync(logDir);
            files.forEach(file => {
                const filePath = path.join(logDir, file);
                const stats = fs.statSync(filePath);
                if (now - stats.mtime.getTime() > maxAge) {
                    fs.unlinkSync(filePath);
                    this.info(`删除旧日志文件: ${file}`);
                }
            });
        }
        catch (error) {
            this.error('清理日志文件失败:', error);
        }
    }
}
// 创建全局日志实例
const logger = new Logger();
// 定期清理日志文件
setInterval(() => {
    logger.cleanupOldLogs();
}, 24 * 60 * 60 * 1000); // 每天清理一次
module.exports = logger;
//# sourceMappingURL=logger.js.map