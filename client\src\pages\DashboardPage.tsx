/**
 * 主控制台页面
 */

import React, { useState, useEffect } from 'react';
import { 
  Layout, 
  Menu, 
  Button, 
  Avatar, 
  Dropdown, 
  Space, 
  Typography,
  Badge,
  Tooltip
} from 'antd';
import {
  DashboardOutlined,
  DesktopOutlined,
  CodeOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  WifiOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { useAuth } from '../hooks/useAuth';
import { useAgents } from '../contexts/AgentContext';
import { wsService } from '../services/websocket';
import AgentListPage from './AgentListPage';
// CommandTerminalPage 已移除，命令执行集中在 AgentListPage 的“命令执行与历史”
import PluginManagePage from './PluginManagePage';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

type PageKey = 'dashboard' | 'agents' | 'plugins' | 'settings';

const DashboardPage: React.FC = () => {
  const { user, logout } = useAuth();
  const { agents } = useAgents();
  const [collapsed, setCollapsed] = useState(false);
  const [currentPage, setCurrentPage] = useState<PageKey>('agents');
  const [wsConnected, setWsConnected] = useState(false);

  // 统计在线Agent数量
  const onlineAgents = Array.isArray(agents) ? agents.filter(agent => agent.status === 'online').length : 0;

  // 监听WebSocket连接状态
  useEffect(() => {
    const unsubscribe = wsService.onConnection((connected) => {
      setWsConnected(connected);
    });

    // 获取当前连接状态
    setWsConnected(wsService.isConnected());

    return unsubscribe;
  }, []);

  /**
   * 处理登出
   */
  const handleLogout = async () => {
    await logout();
  };

  /**
   * 用户下拉菜单
   */
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  /**
   * 侧边栏菜单项
   */
  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '控制台',
    },
    {
      key: 'agents',
      icon: <DesktopOutlined />,
      label: (
        <Space>
          Agent管理
          <Badge count={onlineAgents} size="small" />
        </Space>
      ),
    },
    {
      key: 'plugins',
      icon: <AppstoreOutlined />,
      label: '插件管理',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  /**
   * 渲染页面内容
   */
  const renderContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <div style={{ padding: '24px' }}>
            <Title level={3}>控制台概览</Title>
            <p>欢迎使用 WinRAT 远程访问管理系统</p>
          </div>
        );
      case 'agents':
        return <AgentListPage key="agents-page" />;
      case 'plugins':
        return <PluginManagePage key="plugins-page" />;
      case 'settings':
        return (
          <div style={{ padding: '24px' }}>
            <Title level={3}>系统设置</Title>
            <p>系统设置功能开发中...</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: '#fff',
          boxShadow: '2px 0 8px rgba(0, 0, 0, 0.1)'
        }}
      >
        {/* Logo */}
        <div style={{
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Title 
            level={4} 
            style={{ 
              margin: 0, 
              color: '#1890ff',
              display: collapsed ? 'none' : 'block'
            }}
          >
            WinRAT
          </Title>
          {collapsed && (
            <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
              W
            </Title>
          )}
        </div>

        {/* 菜单 */}
        <Menu
          mode="inline"
          selectedKeys={[currentPage]}
          items={menuItems}
          style={{ borderRight: 0 }}
          onClick={({ key }) => setCurrentPage(key as PageKey)}
        />
      </Sider>

      <Layout>
        {/* 顶部导航 */}
        <Header style={{
          padding: '0 24px',
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
            />
            
            <Tooltip title={`WebSocket连接状态: ${wsConnected ? '已连接' : '未连接'}`}>
              <Badge status={wsConnected ? "success" : "error"} />
              <WifiOutlined style={{ color: wsConnected ? '#52c41a' : '#ff4d4f' }} />
            </Tooltip>
          </Space>

          <Space>
            <span>欢迎，{user?.username}</span>
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
            >
              <Avatar 
                icon={<UserOutlined />} 
                style={{ cursor: 'pointer' }}
              />
            </Dropdown>
          </Space>
        </Header>

        {/* 主内容区 */}
        <Content style={{
          background: '#f5f5f5',
          overflow: 'auto'
        }}>
          {renderContent()}
        </Content>
      </Layout>
    </Layout>
  );
};

export default DashboardPage;
