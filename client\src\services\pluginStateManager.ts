// @ts-nocheck
/**
 * 全局插件状态管理器 (TypeScript migrated)
 */

import apiService from './api';
import systemCommandService from './systemCommandService';
import { fileManagerService } from './fileManagerService';

class PluginStateManager {
  constructor() {
    this.pluginStates = new Map();
    this.ongoingOperations = new Map();
    this.cacheValidityMs = 5 * 60 * 1000;
    this.pluginLoadWaitMs = 3000;
    this.testPaths = {
      fileManager: 'C:\\',
      systemCommand: 'echo test'
    };
  }

  getPluginState(agentId, pluginName) {
    const agentStates = this.pluginStates.get(agentId);
    if (!agentStates) return null;
    const pluginState = agentStates.get(pluginName);
    if (!pluginState) return null;
    const now = Date.now();
    if (now - pluginState.lastChecked > this.cacheValidityMs) {
      agentStates.delete(pluginName);
      if (agentStates.size === 0) this.pluginStates.delete(agentId);
      return null;
    }
    return pluginState;
  }

  setPluginState(agentId, pluginName, status, metadata = {}) {
    if (!this.pluginStates.has(agentId)) {
      this.pluginStates.set(agentId, new Map());
    }
    const agentStates = this.pluginStates.get(agentId);
    agentStates.set(pluginName, {
      status,
      lastChecked: Date.now(),
      ...metadata
    });
  }

  async isPluginAvailable(agentId, pluginName) {
    const cachedState = this.getPluginState(agentId, pluginName);
    if (cachedState) return cachedState.status === 'available';

    const ongoingOp = this.getOngoingOperation(agentId, pluginName);
    if (ongoingOp) {
      try {
        await ongoingOp;
        const newState = this.getPluginState(agentId, pluginName);
        return newState ? newState.status === 'available' : false;
      } catch {
        return false;
      }
    }

    const operation = this.performPluginCheck(agentId, pluginName);
    this.setOngoingOperation(agentId, pluginName, operation);

    try {
      const result = await operation;
      this.clearOngoingOperation(agentId, pluginName);
      return result;
    } catch (error) {
      this.clearOngoingOperation(agentId, pluginName);
      this.setPluginState(agentId, pluginName, 'error', { error: error.message });
      return false;
    }
  }

  async performPluginCheck(agentId, pluginName) {
    try {
      const testResult = await this.executePluginCheck(agentId, pluginName);
      const status = testResult ? 'available' : 'unavailable';
      this.setPluginState(agentId, pluginName, status);
      return testResult;
    } catch (error) {
      this.setPluginState(agentId, pluginName, 'unavailable', { error: error.message });
      return false;
    }
  }

  async executePluginCheck(agentId, pluginName) {
    switch (pluginName) {
      case 'SystemCommandPlugin':
        return await this.checkSystemCommandPlugin(agentId);
      case 'FileManager':
        return await this.checkFileManagerPlugin(agentId);
      default:
        throw new Error(`不支持的插件类型: ${pluginName}`);
    }
  }

  async checkSystemCommandPlugin(agentId) {
    const response = await systemCommandService.executeSystemCommand(
      agentId,
      this.testPaths.systemCommand
    );
    return this.validateSystemCommandResponse(response);
  }

  async checkFileManagerPlugin(agentId) {
    await fileManagerService.ensureInitialized();

    const response = await fileManagerService.executeCommand(
      agentId,
      'file_list',
      { path: this.testPaths.fileManager, includeHidden: false }
    );
    return this.validateFileManagerResponse(response);
  }

  async ensurePluginAvailable(agentId, pluginName, onStatusChange) {
    onStatusChange?.(`正在检查${pluginName}插件...`);

    if (await this.isPluginAvailable(agentId, pluginName)) {
      onStatusChange?.(`${pluginName}插件已就绪`);
      return true;
    }

    onStatusChange?.(`正在部署${pluginName}插件...`);
    this.setPluginState(agentId, pluginName, 'deploying');

    try {
      const pluginsResponse = await apiService.getPlugins();
      if (!pluginsResponse.success) throw new Error('获取插件列表失败');

      const plugins = pluginsResponse.data.plugins || [];
      const targetPlugin = plugins.find(p => p.name === pluginName);
      if (!targetPlugin) throw new Error(`服务器上未找到${pluginName}插件`);

      const deployResponse = await apiService.deployPlugin(targetPlugin.id, agentId);
      if (!deployResponse.success) {
        throw new Error(`${pluginName}插件部署失败: ` + deployResponse.message);
      }

      onStatusChange?.('正在等待插件加载...');
      await new Promise(resolve => setTimeout(resolve, this.pluginLoadWaitMs));

      onStatusChange?.('正在验证插件功能...');
      const isWorking = await this.performPluginCheck(agentId, pluginName);

      if (!isWorking) {
        const errorMsg = '插件部署后验证失败，插件可能未正确加载或初始化';
        this.setPluginState(agentId, pluginName, 'error', { error: errorMsg });
        throw new Error(errorMsg);
      }

      onStatusChange?.(`${pluginName}插件部署并验证完成`);
      return true;

    } catch (error) {
      this.setPluginState(agentId, pluginName, 'error', { error: error.message });
      throw error;
    }
  }

  getOngoingOperation(agentId, pluginName) {
    const agentOps = this.ongoingOperations.get(agentId);
    return agentOps ? agentOps.get(pluginName) : null;
  }

  setOngoingOperation(agentId, pluginName, operation) {
    if (!this.ongoingOperations.has(agentId)) {
      this.ongoingOperations.set(agentId, new Map());
    }
    this.ongoingOperations.get(agentId).set(pluginName, operation);
  }

  clearOngoingOperation(agentId, pluginName) {
    const agentOps = this.ongoingOperations.get(agentId);
    if (agentOps) {
      agentOps.delete(pluginName);
      if (agentOps.size === 0) this.ongoingOperations.delete(agentId);
    }
  }

  clearAgentStates(agentId) {
    this.pluginStates.delete(agentId);
    this.ongoingOperations.delete(agentId);
  }

  clearAllStates() {
    this.pluginStates.clear();
    this.ongoingOperations.clear();
  }

  validateBasicResponse(response, pluginName) {
    if (!response || typeof response !== 'object') return false;
    if (response.success !== true) return false;
    if (!response.data || typeof response.data !== 'object') return false;
    return true;
  }

  validateSystemCommandResponse(response) {
    if (!this.validateBasicResponse(response, 'SystemCommandPlugin')) return false;
    const output = response.data.output || response.data.stdout || '';
    if (!output.includes('test')) return false;
    const exitCode = response.data.exitCode;
    if (exitCode !== 0 && exitCode !== undefined) return false;
    return true;
  }

  validateFileManagerResponse(response) {
    if (!this.validateBasicResponse(response, 'FileManager')) return false;
    const files = response.data.files;
    if (!Array.isArray(files)) return false;
    const path = response.data.path;
    if (!path || typeof path !== 'string') return false;
    return true;
  }

  getStateStats() {
    const stats = {
      totalAgents: this.pluginStates.size,
      totalPlugins: 0,
      statusCounts: {}
    };

    for (const [, agentStates] of this.pluginStates) {
      for (const [, state] of agentStates) {
        stats.totalPlugins++;
        stats.statusCounts[state.status] = (stats.statusCounts[state.status] || 0) + 1;
      }
    }

    return stats;
  }
}

const pluginStateManager = new PluginStateManager();
export default pluginStateManager; 