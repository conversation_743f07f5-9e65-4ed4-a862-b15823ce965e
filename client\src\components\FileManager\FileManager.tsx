// @ts-nocheck
import React, { useState, useEffect } from 'react';
import {
  FolderOutlined,
  FileOutlined,
  ArrowLeftOutlined,
  HomeOutlined,
  DownloadOutlined,
  UploadOutlined,
  DeleteOutlined,
  EditOutlined,
  SearchOutlined,
  PlusOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileImageOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FileZipOutlined
} from '@ant-design/icons';
import { Button, Tooltip, Modal, App, Progress } from 'antd';
import { useFileManager } from '../../hooks/useFileManager';
import FileUploadModal from './FileUploadModal';
import CreateFolderModal from './CreateFolderModal';
import RenameModal from './RenameModal';
import SearchModal from './SearchModal';

const FileManager = ({ agentId, agentName }) => {
  const { modal, message } = App.useApp();

  const {
    currentPath,
    files,
    directories,
    loading,
    error,
    pathHistory,
    listDirectory,
    navigateToPath,
    goBack,
    goHome,
    deleteFile,
    renameFile,
    createDirectory,
    uploadFile,
    searchFiles,
    downloadFile,
    cancelDownload
  } = useFileManager(agentId);

  const [selectedItems, setSelectedItems] = useState([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showCreateFolderModal, setShowCreateModal] = useState(false);
  const [showRenameModal, setShowRenameModal] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [contextMenu, setContextMenu] = useState(null);

  useEffect(() => {
    if (agentId) {
      listDirectory('');
    }
  }, [agentId, listDirectory]);

  const handleItemClick = (item) => {
    if (item.isDirectory) {
      navigateToPath(item.path);
    } else {
      setSelectedItems([item]);
    }
  };

  const handleItemDoubleClick = (item) => {
    if (item.isDirectory) {
      navigateToPath(item.path);
    } else {
      setSelectedItems([item]);
      console.log('准备下载文件:', item.path);
    }
  };

  const handleContextMenu = (e, item) => {
    e.preventDefault();
    setContextMenu({ x: e.clientX, y: e.clientY, item });
  };

  const closeContextMenu = () => setContextMenu(null);

  const getFileIcon = (fileName, isDirectory) => {
    if (isDirectory) {
      return <FolderOutlined style={{ color: '#1890ff', fontSize: '18px' }} />;
    }
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'txt':
      case 'log':
        return <FileTextOutlined style={{ color: '#52c41a', fontSize: '18px' }} />;
      case 'exe':
      case 'msi':
        return <FileOutlined style={{ color: '#f5222d', fontSize: '18px' }} />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <FileImageOutlined style={{ color: '#722ed1', fontSize: '18px' }} />;
      case 'pdf':
        return <FilePdfOutlined style={{ color: '#f5222d', fontSize: '18px' }} />;
      case 'doc':
      case 'docx':
        return <FileWordOutlined style={{ color: '#1890ff', fontSize: '18px' }} />;
      case 'xls':
      case 'xlsx':
        return <FileExcelOutlined style={{ color: '#52c41a', fontSize: '18px' }} />;
      case 'zip':
      case 'rar':
      case '7z':
        return <FileZipOutlined style={{ color: '#fa8c16', fontSize: '18px' }} />;
      default:
        return <FileOutlined style={{ color: '#8c8c8c', fontSize: '18px' }} />;
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleStreamDownload = async (filePath, fileName) => {
    let progressModal;
    let downloadCancelled = false;
    let currentTransferId = null;

    try {
      const createProgressContent = (progress = { progress: 0, speed: 0, elapsed: 0, downloadedChunks: 0, totalChunks: 0 }) => (
        <div style={{ padding: '20px 0' }}>
          <div style={{ marginBottom: '16px' }}>
            <strong>文件:</strong> {fileName}
          </div>
          <div style={{ marginBottom: '16px' }}>
            <Progress
              percent={progress.progress}
              status="active"
              strokeColor={{ '0%': '#108ee9', '100%': '#87d068' }}
            />
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', color: '#666' }}>
            <span>速度: {progress.speed} KB/s</span>
            <span>已用时间: {progress.elapsed}s</span>
          </div>
          {progress.totalChunks > 0 && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
              {progress.downloadedChunks}/{progress.totalChunks} 数据块
            </div>
          )}
        </div>
      );

      progressModal = modal.info({
        title: '文件下载中',
        content: createProgressContent(),
        okText: '取消下载',
        onOk: async () => {
          downloadCancelled = true;
          if (progressModal) {
            progressModal.destroy();
            progressModal = null;
          }
          if (currentTransferId) {
            setTimeout(async () => {
              try {
                await cancelDownload(agentId, currentTransferId);
              } catch (error) {
                console.log('Agent端会话清理结果:', error.message);
              }
            }, 100);
          }
        },
        width: 480,
        maskClosable: false
      });

      const success = await downloadFile(
        filePath,
        fileName,
        (progress) => {
          if (progress.transferId && !currentTransferId) {
            currentTransferId = progress.transferId;
          }
          if (progressModal && !downloadCancelled) {
            progressModal.update({ content: createProgressContent(progress) });
          }
        },
        () => downloadCancelled
      );

      if (downloadCancelled) {
        return;
      } else if (success) {
        message.success(`文件 "${fileName}" 下载完成`);
      }

    } catch (error) {
      console.error('下载过程中出现错误:', error);
      if (downloadCancelled || error.message.includes('下载已取消')) {
        return;
      }
      message.error(`下载失败: ${error.message}`);
    } finally {
      if (progressModal) {
        progressModal.destroy();
        progressModal = null;
      }
    }
  };

  const parseBreadcrumbPaths = (path) => {
    if (!path || path === '' || path === 'drives://') {
      return { drive: null, paths: [], isDriveList: true };
    }
    const parts = path.split('\\').filter(Boolean);
    if (parts.length === 0) return { drive: null, paths: [], isDriveList: true };
    const drive = parts[0] + ':';
    const paths = parts.slice(1);
    return { drive, paths, isDriveList: false };
  };

  const { drive, paths: breadcrumbPaths, isDriveList } = parseBreadcrumbPaths(currentPath);

  if (!agentId) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
        <div style={{ color: '#999' }}>等待Agent连接...</div>
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%', background: 'white' }}>
      {/* 工具栏 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '12px', borderBottom: '1px solid #f0f0f0', background: '#fafafa' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Button icon={<ArrowLeftOutlined />} onClick={goBack} disabled={pathHistory.length <= 1} size="small" />
          <Button icon={<HomeOutlined />} onClick={goHome} size="small" />
          <span style={{ fontSize: '14px', fontWeight: 500, color: '#666', marginLeft: '8px' }}>{agentName} - 文件管理器</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Tooltip title="搜索文件">
            <Button icon={<SearchOutlined />} onClick={() => setShowSearchModal(true)} size="small" />
          </Tooltip>
          <Tooltip title="新建文件夹">
            <Button icon={<PlusOutlined />} onClick={() => setShowCreateModal(true)} size="small" />
          </Tooltip>
          <Tooltip title="上传文件">
            <Button icon={<UploadOutlined />} onClick={() => setShowUploadModal(true)} size="small" />
          </Tooltip>
          {selectedItems.length > 0 && (
            <>
              <Tooltip title="流式下载">
                <Button icon={<DownloadOutlined />} onClick={() => {
                  if (selectedItems.length === 1 && !selectedItems[0].isDirectory) {
                    handleStreamDownload(selectedItems[0].path, selectedItems[0].name);
                  } else if (selectedItems.length === 1 && selectedItems[0].isDirectory) {
                    modal.info({ title: '不支持下载目录', content: '请选择单个文件进行下载。目录下载功能正在开发中。' });
                  } else {
                    modal.info({ title: '不支持多文件下载', content: '请选择单个文件进行下载。批量下载功能正在开发中。' });
                  }
                }} size="small" />
              </Tooltip>
              <Tooltip title="重命名">
                <Button icon={<EditOutlined />} onClick={() => setShowRenameModal(true)} size="small" />
              </Tooltip>
              <Tooltip title="删除">
                <Button icon={<DeleteOutlined />} onClick={() => { modal.confirm({ title: '确认删除', content: '确定要删除选中的文件吗？', onOk: () => { selectedItems.forEach(item => deleteFile(item.path)); setSelectedItems([]); } }); }} danger size="small" />
              </Tooltip>
            </>
          )}
        </div>
      </div>

      {/* 面包屑导航 */}
      <div style={{ display: 'flex', alignItems: 'center', padding: '12px', background: '#f5f5f5', borderBottom: '1px solid #d9d9d9' }}>
        <span style={{ fontSize: '12px', color: '#666', marginRight: '8px' }}>位置:</span>
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          {isDriveList ? (
            <span style={{ fontSize: '12px', color: '#333', fontWeight: 'bold' }}>此电脑 - 所有驱动器</span>
          ) : (
            <>
              <Button type="link" size="small" onClick={() => navigateToPath(drive.replace(':', '') + '\\')} style={{ padding: '0 4px', height: 'auto', fontSize: '12px' }}>
                {drive}
              </Button>
              {breadcrumbPaths.map((part, index) => (
                <React.Fragment key={index}>
                  <span style={{ color: '#bfbfbf' }}>\\</span>
                  <Button type="link" size="small" onClick={() => { const path = drive.replace(':', '') + '\\' + breadcrumbPaths.slice(0, index + 1).join('\\'); navigateToPath(path); }} style={{ padding: '0 4px', height: 'auto', fontSize: '12px' }}>
                    {part}
                  </Button>
                </React.Fragment>
              ))}
            </>
          )}
        </div>
      </div>

      {/* 文件列表 */}
      <div style={{ flex: 1, overflow: 'auto' }} onClick={closeContextMenu}>
        {loading ? (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '200px' }}>
            <div style={{ color: '#999' }}>加载中...</div>
          </div>
        ) : error ? (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '200px' }}>
            <div style={{ color: '#ff4d4f' }}>错误: {error}</div>
          </div>
        ) : (
          <div style={{ padding: '16px' }}>
            {/* 目录列表 */}
            {directories.map((dir) => (
              <div key={dir.path} style={{ display: 'flex', alignItems: 'center', padding: '12px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0', transition: 'background-color 0.2s' }}
                onClick={() => handleItemClick(dir)} onDoubleClick={() => handleItemDoubleClick(dir)} onContextMenu={(e) => handleContextMenu(e, dir)}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#f5f5f5'} onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}>
                {getFileIcon(dir.name, true)}
                <div style={{ marginLeft: '12px', flex: 1 }}>
                  <div style={{ fontWeight: 500 }}>{dir.name}</div>
                  <div style={{ fontSize: '12px', color: '#999' }}>
                    {dir.driveType ? (<>{dir.driveType} • {dir.freeSpaceFormatted} 可用，共 {dir.totalSpaceFormatted}</>) : (dir.lastModified)}
                  </div>
                </div>
                <div style={{ fontSize: '12px', color: '#bfbfbf' }}>{dir.driveType ? '驱动器' : '文件夹'}</div>
              </div>
            ))}

            {/* 文件列表 */}
            {files.map((file) => (
              <div key={file.path} style={{ display: 'flex', alignItems: 'center', padding: '12px', cursor: 'pointer', borderBottom: '1px solid #f0f0f0', backgroundColor: selectedItems.some(item => item.path === file.path) ? '#e6f7ff' : 'transparent', transition: 'background-color 0.2s' }}
                onClick={() => handleItemClick(file)} onDoubleClick={() => handleItemDoubleClick(file)} onContextMenu={(e) => handleContextMenu(e, file)}
                onMouseEnter={(e) => { if (!selectedItems.some(item => item.path === file.path)) { e.target.style.backgroundColor = '#f5f5f5'; } }}
                onMouseLeave={(e) => { if (!selectedItems.some(item => item.path === file.path)) { e.target.style.backgroundColor = 'transparent'; } }}>
                {getFileIcon(file.name, false)}
                <div style={{ marginLeft: '12px', flex: 1 }}>
                  <div style={{ fontWeight: 500 }}>{file.name}</div>
                  <div style={{ fontSize: '12px', color: '#999' }}>{file.lastModified}</div>
                </div>
                <div style={{ fontSize: '12px', color: '#bfbfbf' }}>{formatFileSize(file.size)}</div>
              </div>
            ))}

            {directories.length === 0 && files.length === 0 && (
              <div style={{ textAlign: 'center', color: '#999', padding: '32px' }}>此文件夹为空</div>
            )}
          </div>
        )}
      </div>

      {/* 状态栏 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '8px 16px', borderTop: '1px solid #d9d9d9', background: '#fafafa', fontSize: '12px', color: '#666' }}>
        <div>{directories.length} 个文件夹, {files.length} 个文件</div>
        <div>{selectedItems.length > 0 && `已选择 ${selectedItems.length} 项`}</div>
      </div>

      {/* 右键菜单 */}
      {contextMenu && (
        <div style={{ position: 'fixed', left: contextMenu.x, top: contextMenu.y, background: 'white', border: '1px solid #d9d9d9', borderRadius: '6px', boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)', zIndex: 1000, minWidth: '120px' }}>
          <div onClick={() => { setSelectedItems([contextMenu.item]); if (!contextMenu.item.isDirectory) { handleStreamDownload(contextMenu.item.path, contextMenu.item.name); } else { modal.info({ title: '不支持下载目录', content: '请选择文件进行下载。目录下载功能正在开发中。' }); } closeContextMenu(); }} style={{ padding: '8px 16px', cursor: 'pointer', fontSize: '14px', borderBottom: '1px solid #f0f0f0' }} onMouseEnter={(e) => e.target.style.backgroundColor = '#f5f5f5'} onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}>
            下载
          </div>
          <div onClick={() => { setSelectedItems([contextMenu.item]); setShowRenameModal(true); closeContextMenu(); }} style={{ padding: '8px 16px', cursor: 'pointer', fontSize: '14px', borderBottom: '1px solid #f0f0f0' }} onMouseEnter={(e) => e.target.style.backgroundColor = '#f5f5f5'} onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}>
            重命名
          </div>
          <div onClick={() => { modal.confirm({ title: '确认删除', content: '确定要删除此文件吗？', onOk: () => { deleteFile(contextMenu.item.path); closeContextMenu(); } }); }} style={{ padding: '8px 16px', cursor: 'pointer', fontSize: '14px', color: '#ff4d4f' }} onMouseEnter={(e) => e.target.style.backgroundColor = '#fff2f0'} onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}>
            删除
          </div>
        </div>
      )}

      {/* 模态框 */}
      {showUploadModal && (
        <FileUploadModal currentPath={currentPath} onClose={() => setShowUploadModal(false)} onUpload={(file) => { uploadFile(file, currentPath); setShowUploadModal(false); }} />
      )}

      {showCreateFolderModal && (
        <CreateFolderModal currentPath={currentPath} onClose={() => setShowCreateModal(false)} onCreate={(name) => { createDirectory(currentPath + '\\' + name); setShowCreateModal(false); }} />
      )}

      {showRenameModal && selectedItems.length > 0 && (
        <RenameModal item={selectedItems[0]} onClose={() => setShowRenameModal(false)} onRename={(oldPath, newName) => {
          const separator = oldPath.includes('\\') ? '\\' : '/';
          const lastSeparatorIndex = oldPath.lastIndexOf(separator);
          let newPath;
          if (lastSeparatorIndex === -1) {
            newPath = newName;
          } else {
            const directory = oldPath.substring(0, lastSeparatorIndex + 1);
            newPath = directory + newName;
          }
          renameFile(oldPath, newPath);
          setShowRenameModal(false);
          setSelectedItems([]);
        }} />
      )}

      {showSearchModal && (
        <SearchModal currentPath={currentPath} onClose={() => setShowSearchModal(false)} onSearch={(pattern) => { searchFiles(currentPath, pattern); setShowSearchModal(false); }} />
      )}
    </div>
  );
};

export default FileManager; 