// @ts-nocheck
import React, { useState, useRef } from 'react';
import { CloseOutlined, CloudUploadOutlined } from '@ant-design/icons';
import { Button, Modal, Progress } from 'antd';

const FileUploadModal = ({ currentPath, onClose, onUpload }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef(null);

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleDrop = (event) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      await onUpload(selectedFile);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      
      setTimeout(() => {
        onClose();
      }, 500);
    } catch (error) {
      console.error('上传失败:', error);
      alert('上传失败: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Modal
      title="上传文件"
      open={true}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose} disabled={uploading}>
          取消
        </Button>,
        <Button
          key="upload"
          type="primary"
          onClick={handleUpload}
          disabled={!selectedFile || uploading}
          loading={uploading}
        >
          {uploading ? '上传中...' : '上传'}
        </Button>
      ]}
      width={480}
    >

        {/* 内容 */}
        <div className="p-4">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              目标路径:
            </label>
            <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
              {currentPath}
            </div>
          </div>

          {/* 文件选择区域 */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center ${
              selectedFile ? 'border-green-300 bg-green-50' : 'border-gray-300'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
          >
            {selectedFile ? (
              <div style={{ textAlign: 'center' }}>
                <CloudUploadOutlined style={{ fontSize: '48px', color: '#52c41a', marginBottom: '8px' }} />
                <div style={{ fontWeight: 500, marginBottom: '4px' }}>{selectedFile.name}</div>
                <div style={{ fontSize: '12px', color: '#999', marginBottom: '8px' }}>
                  {formatFileSize(selectedFile.size)}
                </div>
                <Button
                  type="link"
                  onClick={() => setSelectedFile(null)}
                  size="small"
                >
                  选择其他文件
                </Button>
              </div>
            ) : (
              <div style={{ textAlign: 'center' }}>
                <CloudUploadOutlined style={{ fontSize: '48px', color: '#bfbfbf', marginBottom: '8px' }} />
                <div style={{ color: '#666', marginBottom: '12px' }}>
                  拖拽文件到此处或点击选择
                </div>
                <Button
                  type="primary"
                  onClick={() => fileInputRef.current?.click()}
                >
                  选择文件
                </Button>
              </div>
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            onChange={handleFileSelect}
            className="hidden"
          />

          {/* 上传进度 */}
          {uploading && (
            <div style={{ marginTop: '16px' }}>
              <Progress percent={uploadProgress} status="active" />
            </div>
          )}
        </div>
    </Modal>
  );
};

export default FileUploadModal; 